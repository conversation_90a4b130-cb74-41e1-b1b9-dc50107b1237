// cloudfunctions/getUserStats/index.js
// 获取用户统计信息云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { openid } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    // 获取总聊天次数
    const chatCountResult = await db.collection('chat_messages').where({
      openid: currentOpenid,
      role: 'user'
    }).count()

    // 获取剩余额度
    const subscriptionResult = await db.collection('subscriptions').where({
      openid: currentOpenid,
      status: 'active'
    }).orderBy('createTime', 'desc').limit(1).get()

    let quotaRemaining = 10 // 默认免费额度
    
    if (subscriptionResult.data.length > 0) {
      const subscription = subscriptionResult.data[0]
      const now = new Date()
      
      if (subscription.expireTime && new Date(subscription.expireTime) > now) {
        quotaRemaining = (subscription.quotaTotal || 0) - (subscription.quotaUsed || 0)
      } else {
        // 订阅过期，使用免费额度
        const usageCount = await db.collection('usage_logs').where({
          openid: currentOpenid
        }).count()
        quotaRemaining = Math.max(0, 10 - usageCount.total)
      }
    } else {
      // 没有订阅，计算免费额度使用情况
      const usageCount = await db.collection('usage_logs').where({
        openid: currentOpenid
      }).count()
      quotaRemaining = Math.max(0, 10 - usageCount.total)
    }

    // 获取最常用的AI模型
    const modelUsageResult = await db.collection('usage_logs').where({
      openid: currentOpenid
    }).get()

    let favoriteModel = 'DeepSeek-V3'
    if (modelUsageResult.data.length > 0) {
      const modelCount = {}
      modelUsageResult.data.forEach(log => {
        const model = log.model || 'deepseek-v3'
        modelCount[model] = (modelCount[model] || 0) + 1
      })
      
      const sortedModels = Object.entries(modelCount).sort((a, b) => b[1] - a[1])
      if (sortedModels.length > 0) {
        const modelMap = {
          'deepseek-v3': 'DeepSeek-V3',
          'deepseek-r1': 'DeepSeek-R1',
          'chatgpt': 'ChatGPT',
          'gemini': 'Gemini',
          'qwen3': 'Qwen3',
          'qwen-max': 'Qwen Max'
        }
        favoriteModel = modelMap[sortedModels[0][0]] || 'DeepSeek-V3'
      }
    }

    // 获取本月聊天次数
    const now = new Date()
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const monthChatResult = await db.collection('chat_messages').where({
      openid: currentOpenid,
      role: 'user',
      createTime: db.command.gte(monthStart)
    }).count()

    // 获取连续使用天数
    const recentDaysResult = await db.collection('usage_logs').where({
      openid: currentOpenid
    }).orderBy('createTime', 'desc').limit(30).get()

    let consecutiveDays = 0
    if (recentDaysResult.data.length > 0) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const usageDates = new Set()
      recentDaysResult.data.forEach(log => {
        const logDate = new Date(log.createTime)
        logDate.setHours(0, 0, 0, 0)
        usageDates.add(logDate.getTime())
      })
      
      const sortedDates = Array.from(usageDates).sort((a, b) => b - a)
      
      for (let i = 0; i < sortedDates.length; i++) {
        const expectedDate = new Date(today.getTime() - i * 24 * 60 * 60 * 1000)
        if (sortedDates[i] === expectedDate.getTime()) {
          consecutiveDays++
        } else {
          break
        }
      }
    }

    const stats = {
      totalChats: chatCountResult.total,
      quotaRemaining: quotaRemaining,
      favoriteModel: favoriteModel,
      monthlyChats: monthChatResult.total,
      consecutiveDays: consecutiveDays
    }

    return {
      success: true,
      stats: stats
    }
  } catch (error) {
    console.error('Get user stats error:', error)
    return {
      success: false,
      error: error.message,
      stats: {
        totalChats: 0,
        quotaRemaining: 0,
        favoriteModel: 'DeepSeek-V3',
        monthlyChats: 0,
        consecutiveDays: 0
      }
    }
  }
}
