/* pages/chat/chat.wxss */

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F5F7FA;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E9ECEF;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  flex: 1;
}

.session-info {
  display: flex;
  flex-direction: column;
}

.session-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 8rpx;
}

.session-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #6C757D;
}

.separator {
  margin: 0 12rpx;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quota-info {
  padding: 8rpx 16rpx;
  background-color: #E3F2FD;
  border-radius: 12rpx;
}

.quota-text {
  font-size: 24rpx;
  color: #1976D2;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background-color: #F8F9FA;
  transition: background-color 0.3s ease;
}

.action-btn:active {
  background-color: #E9ECEF;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

/* 欢迎消息 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.welcome-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}

.avatar-img {
  width: 80rpx;
  height: 80rpx;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 16rpx;
}

.welcome-desc {
  font-size: 28rpx;
  color: #6C757D;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.quick-questions {
  width: 100%;
}

.quick-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 24rpx;
}

.quick-item {
  background-color: #FFFFFF;
  border: 2rpx solid #E9ECEF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.quick-item:active {
  background-color: #F8F9FA;
  border-color: #2E86AB;
  transform: scale(0.98);
}

.question-text {
  font-size: 28rpx;
  color: #495057;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.question-type {
  font-size: 22rpx;
  color: #2E86AB;
  background-color: #E3F2FD;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

/* 消息列表 */
.message-list {
  padding-bottom: 40rpx;
}

.message-item {
  margin-bottom: 32rpx;
}

/* 用户消息 */
.message-user {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 16rpx;
}

.message-user .message-content {
  max-width: 70%;
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
  border-radius: 20rpx 20rpx 8rpx 20rpx;
  padding: 24rpx;
}

.message-user .message-text {
  font-size: 30rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-user .message-time {
  font-size: 22rpx;
  opacity: 0.8;
  margin-top: 12rpx;
  text-align: right;
}

.message-user .message-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

/* AI消息 */
.message-ai {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.message-ai .message-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-ai .message-content {
  flex: 1;
  max-width: 70%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.ai-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #2E86AB;
}

.message-actions {
  display: flex;
  gap: 8rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 16rpx;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
}

.action-item:active {
  background-color: #E9ECEF;
  transform: scale(0.95);
}

.action-item .action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.action-text {
  font-size: 22rpx;
  color: #6C757D;
}

.message-ai .message-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #212529;
  word-wrap: break-word;
}

.message-ai .message-time {
  font-size: 22rpx;
  color: #6C757D;
  margin-top: 12rpx;
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.typing-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.typing-dots {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #2E86AB;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  font-size: 24rpx;
  color: #6C757D;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-input-area {
  background-color: #FFFFFF;
  border-top: 1rpx solid #E9ECEF;
  padding: 20rpx;
}

/* 咨询类型选择器 */
.consultation-selector {
  margin-bottom: 20rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
}

.selector-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 20rpx;
}

.consultation-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.option-item.selected {
  border-color: #2E86AB;
  background-color: #E3F2FD;
}

.option-item:active {
  transform: scale(0.95);
}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-text {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 22rpx;
  color: #6C757D;
  line-height: 1.3;
}

/* 输入容器 */
.input-container {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
}

.input-wrapper {
  flex: 1;
  background-color: #F8F9FA;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: flex-end;
  gap: 12rpx;
}

.message-input {
  flex: 1;
  font-size: 30rpx;
  color: #212529;
  background-color: transparent;
  border: none;
  outline: none;
  min-height: 48rpx;
  max-height: 200rpx;
  line-height: 1.5;
}

.input-actions {
  display: flex;
  gap: 8rpx;
}

.input-actions .action-item {
  width: 48rpx;
  height: 48rpx;
  background-color: transparent;
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-btn:not(.active) {
  background: #E9ECEF;
}

.send-btn:active {
  transform: scale(0.95);
}

.send-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 模型选择器 */
.model-selector {
  width: 80%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.selector-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.model-item.selected {
  border-color: #2E86AB;
  background-color: #E3F2FD;
}

.model-item.disabled {
  opacity: 0.5;
}

.model-item:not(.disabled):active {
  transform: scale(0.98);
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.model-desc {
  font-size: 26rpx;
  color: #6C757D;
  margin-bottom: 8rpx;
}

.model-tier {
  font-size: 24rpx;
  color: #FF6B35;
}

.model-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.status-dot.available {
  background-color: #28A745;
}

.status-dot.unavailable {
  background-color: #DC3545;
}

/* 会话菜单 */
.session-menu {
  width: 300rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx 0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  transition: background-color 0.3s ease;
}

.menu-item:active {
  background-color: #F8F9FA;
}

.menu-item.danger {
  color: #DC3545;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 30rpx;
  color: #212529;
}

.menu-item.danger .menu-text {
  color: #DC3545;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F3F3F3;
  border-top: 6rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6C757D;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* IVD术语高亮样式 */
.ivd-term {
  color: #2E86AB !important;
  font-weight: 500 !important;
  text-decoration: underline !important;
  cursor: pointer !important;
  padding: 2rpx 4rpx;
  border-radius: 4rpx;
  background-color: rgba(46, 134, 171, 0.1);
  transition: background-color 0.3s ease;
}

.ivd-term:hover {
  background-color: rgba(46, 134, 171, 0.2);
}
