/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  box-sizing: border-box;
  position: relative;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  padding: 80rpx 0 60rpx;
  color: #FFFFFF;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  border-radius: 32rpx;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.app-slogan {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 登录表单 */
.login-form {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
}

.form-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #2E86AB;
  text-align: center;
  margin-bottom: 16rpx;
}

.form-subtitle {
  font-size: 28rpx;
  color: #6C757D;
  text-align: center;
  margin-bottom: 60rpx;
}

/* 微信登录按钮 */
.wx-login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #07C160 0%, #00AE66 100%);
  color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.wx-login-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.login-time {
  font-size: 24rpx;
  color: #6C757D;
}

.status-badge {
  padding: 8rpx 16rpx;
  background-color: #28A745;
  color: #FFFFFF;
  border-radius: 12rpx;
  font-size: 24rpx;
}

/* 资料完善区域 */
.profile-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.label {
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #DEE2E6;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #2E86AB;
}

/* 兴趣标签 */
.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  padding: 16rpx 24rpx;
  border: 2rpx solid #DEE2E6;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #6C757D;
  background-color: #FFFFFF;
  transition: all 0.3s ease;
}

.tag.selected {
  background-color: #2E86AB;
  color: #FFFFFF;
  border-color: #2E86AB;
}

.tag:active {
  transform: scale(0.95);
}

/* 保存按钮 */
.save-profile-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin-top: 40rpx;
}

.save-profile-btn:disabled {
  background: #E9ECEF;
  color: #ADB5BD;
}

/* 服务协议 */
.agreement-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #6C757D;
  line-height: 1.6;
}

.link {
  color: #2E86AB;
  text-decoration: underline;
}

/* 功能介绍 */
.features-section {
  margin-bottom: 40rpx;
}

.feature-list {
  margin-top: 32rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F1F3F4;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 12rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #6C757D;
  line-height: 1.5;
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding: 0 40rpx 40rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
}

.action-btn.secondary {
  background-color: #F8F9FA;
  color: #6C757D;
  border: 2rpx solid #DEE2E6;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F3F3F3;
  border-top: 6rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6C757D;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
