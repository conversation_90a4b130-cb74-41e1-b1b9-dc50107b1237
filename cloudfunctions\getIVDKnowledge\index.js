// cloudfunctions/getIVDKnowledge/index.js
// IVD专业知识库云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// IVD专业知识库
const IVD_KNOWLEDGE_BASE = {
  // 技术平台知识
  technologies: {
    immunoassay: {
      name: '免疫检测技术',
      types: ['ELISA', '化学发光', '免疫层析', '免疫荧光', '电化学发光'],
      principles: '基于抗原抗体特异性结合反应的检测技术',
      applications: ['感染性疾病', '肿瘤标志物', '心脏标志物', '激素检测'],
      advantages: ['特异性强', '灵敏度高', '操作简便', '成本适中'],
      limitations: ['交叉反应', '基质效应', '钩状效应']
    },
    molecular: {
      name: '分子诊断技术',
      types: ['PCR', 'qPCR', 'NGS', '基因芯片', 'LAMP', 'CRISPR'],
      principles: '基于核酸序列特异性检测的技术',
      applications: ['病原体检测', '遗传病诊断', '肿瘤基因检测', '药物基因组学'],
      advantages: ['特异性极强', '灵敏度极高', '可定量检测', '多重检测'],
      limitations: ['成本较高', '技术要求高', '污染风险', '设备依赖']
    },
    biochemistry: {
      name: '生化检测技术',
      types: ['酶法', '离子选择电极', '比色法', '荧光法', '电化学法'],
      principles: '基于生化反应的检测技术',
      applications: ['肝功能', '肾功能', '血脂', '血糖', '电解质'],
      advantages: ['成熟稳定', '成本低廉', '通量高', '标准化程度高'],
      limitations: ['特异性相对较低', '易受干扰', '需要大型设备']
    }
  },

  // 法规知识
  regulations: {
    nmpa: {
      name: 'NMPA注册管理',
      classification: {
        'class1': '第一类：风险程度低，实行产品备案管理',
        'class2': '第二类：具有中度风险，实行产品注册管理',
        'class3': '第三类：具有较高风险，实行产品注册管理'
      },
      process: [
        '产品分类界定',
        '注册检验',
        '临床评价',
        '质量管理体系核查',
        '注册申报',
        '技术审评',
        '行政审批'
      ],
      timeline: {
        'class2': '120个工作日',
        'class3': '180个工作日'
      }
    },
    fda: {
      name: 'FDA认证管理',
      pathways: {
        '510k': '实质等同性认证，适用于大多数IVD产品',
        'pma': '上市前批准，适用于高风险创新产品',
        'de_novo': '新型产品分类途径'
      },
      timeline: {
        '510k': '90天',
        'pma': '180天',
        'de_novo': '120天'
      }
    },
    ce: {
      name: 'CE标识认证',
      directive: 'IVDR 2017/746',
      classes: ['Class A', 'Class B', 'Class C', 'Class D'],
      conformity_routes: [
        '制造商自我声明',
        '公告机构参与评估',
        '全面质量保证'
      ]
    }
  },

  // 市场知识
  market: {
    segments: {
      hospital: {
        name: '医院市场',
        characteristics: ['采购集中', '技术要求高', '价格敏感度中等'],
        decision_makers: ['检验科主任', '设备科', '院长'],
        sales_cycle: '3-12个月'
      },
      laboratory: {
        name: '第三方检验',
        characteristics: ['成本敏感', '通量要求高', '自动化程度高'],
        decision_makers: ['实验室总监', '采购经理', '技术总监'],
        sales_cycle: '6-18个月'
      },
      poct: {
        name: 'POCT市场',
        characteristics: ['便携性要求', '操作简便', '快速出结果'],
        decision_makers: ['科室主任', '护士长', '医生'],
        sales_cycle: '1-6个月'
      }
    },
    trends: [
      '自动化程度提升',
      '检测项目整合',
      '成本控制压力',
      '质量要求提高',
      '个性化医疗发展',
      '人工智能应用'
    ]
  },

  // 质量管理知识
  quality: {
    iso13485: {
      name: 'ISO 13485质量管理体系',
      requirements: [
        '质量管理体系',
        '管理职责',
        '资源管理',
        '产品实现',
        '测量、分析和改进'
      ],
      key_processes: [
        '设计控制',
        '风险管理',
        '供应商管理',
        '生产控制',
        '纠正预防措施'
      ]
    },
    risk_management: {
      standard: 'ISO 14971',
      process: [
        '风险分析',
        '风险评价',
        '风险控制',
        '剩余风险评价',
        '风险管理报告'
      ],
      methods: ['FMEA', 'FTA', '风险矩阵', 'HAZOP']
    }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { query, category, type } = event
  
  try {
    console.log('查询IVD知识库:', { query, category, type })
    
    let result = {}
    
    if (category && type) {
      // 精确查询特定知识
      result = getSpecificKnowledge(category, type)
    } else if (query) {
      // 模糊搜索相关知识
      result = searchKnowledge(query)
    } else {
      // 返回知识库概览
      result = getKnowledgeOverview()
    }
    
    return {
      success: true,
      knowledge: result,
      timestamp: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('获取IVD知识失败:', error)
    return {
      success: false,
      error: error.message || '知识库查询失败'
    }
  }
}

// 获取特定知识
function getSpecificKnowledge(category, type) {
  const categoryData = IVD_KNOWLEDGE_BASE[category]
  if (!categoryData) {
    throw new Error(`未找到分类: ${category}`)
  }
  
  const typeData = categoryData[type]
  if (!typeData) {
    throw new Error(`未找到类型: ${type}`)
  }
  
  return {
    category: category,
    type: type,
    data: typeData
  }
}

// 搜索知识
function searchKnowledge(query) {
  const results = []
  const queryLower = query.toLowerCase()
  
  // 遍历所有知识库内容
  for (const [categoryKey, categoryValue] of Object.entries(IVD_KNOWLEDGE_BASE)) {
    for (const [typeKey, typeValue] of Object.entries(categoryValue)) {
      // 检查是否匹配
      if (isMatchingContent(typeValue, queryLower)) {
        results.push({
          category: categoryKey,
          type: typeKey,
          data: typeValue,
          relevance: calculateRelevance(typeValue, queryLower)
        })
      }
    }
  }
  
  // 按相关性排序
  results.sort((a, b) => b.relevance - a.relevance)
  
  return {
    query: query,
    results: results.slice(0, 10), // 返回前10个最相关的结果
    total: results.length
  }
}

// 检查内容是否匹配
function isMatchingContent(content, query) {
  const contentStr = JSON.stringify(content).toLowerCase()
  return contentStr.includes(query)
}

// 计算相关性得分
function calculateRelevance(content, query) {
  const contentStr = JSON.stringify(content).toLowerCase()
  const queryWords = query.split(' ')
  let score = 0
  
  queryWords.forEach(word => {
    const matches = (contentStr.match(new RegExp(word, 'g')) || []).length
    score += matches
  })
  
  return score
}

// 获取知识库概览
function getKnowledgeOverview() {
  const overview = {}
  
  for (const [categoryKey, categoryValue] of Object.entries(IVD_KNOWLEDGE_BASE)) {
    overview[categoryKey] = {
      name: getCategoryName(categoryKey),
      types: Object.keys(categoryValue),
      count: Object.keys(categoryValue).length
    }
  }
  
  return {
    overview: overview,
    total_categories: Object.keys(IVD_KNOWLEDGE_BASE).length,
    total_items: Object.values(IVD_KNOWLEDGE_BASE).reduce((sum, cat) => sum + Object.keys(cat).length, 0)
  }
}

// 获取分类名称
function getCategoryName(categoryKey) {
  const names = {
    technologies: '技术平台',
    regulations: '法规管理',
    market: '市场分析',
    quality: '质量管理'
  }
  return names[categoryKey] || categoryKey
}
