/* pages/profile/profile.wxss */

.profile-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #2E86AB 0%, #F5F7FA 40%);
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  margin: 40rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 134, 171, 0.15);
}

.user-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.avatar-section {
  position: relative;
  margin-right: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  border: 2rpx solid #FFFFFF;
}

.badge-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12rpx;
}

.user-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #6C757D;
  margin-bottom: 8rpx;
}

.separator {
  margin: 0 12rpx;
}

.user-profile {
  font-size: 26rpx;
  color: #495057;
}

.real-name {
  font-weight: 500;
}

.company {
  color: #6C757D;
}

.edit-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  transition: background-color 0.3s ease;
}

.edit-btn:active {
  background-color: #E9ECEF;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 订阅状态 */
.subscription-status {
  display: flex;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.status-item {
  text-align: center;
  flex: 1;
}

.status-label {
  font-size: 24rpx;
  color: #6C757D;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
}

.status-value.active {
  color: #2E86AB;
}

.status-value.free {
  color: #6C757D;
}

/* 使用统计 */
.usage-stats {
  margin-bottom: 32rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6C757D;
}

/* 功能菜单 */
.menu-section {
  margin: 0 32rpx;
}

.menu-group {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #F8F9FA;
}

.menu-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-icon-wrapper.subscription {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
}

.menu-icon-wrapper.chat {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
}

.menu-icon-wrapper.usage {
  background: linear-gradient(135deg, #28A745 0%, #20C997 100%);
}

.menu-icon-wrapper.settings {
  background: linear-gradient(135deg, #6F42C1 0%, #E83E8C 100%);
}

.menu-icon-wrapper.feedback {
  background: linear-gradient(135deg, #FD7E14 0%, #E83E8C 100%);
}

.menu-icon-wrapper.about {
  background: linear-gradient(135deg, #6C757D 0%, #495057 100%);
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #6C757D;
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 24rpx;
  margin: 40rpx 32rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
}

.action-btn.secondary {
  background-color: #FFFFFF;
  color: #2E86AB;
  border: 2rpx solid #2E86AB;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 32rpx;
}

.logout-btn {
  width: 100%;
  padding: 32rpx;
  background-color: #FFFFFF;
  color: #DC3545;
  border: 2rpx solid #DC3545;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #DC3545;
  color: #FFFFFF;
}

/* 版本信息 */
.version-info {
  text-align: center;
  margin: 40rpx 0;
}

.version-text {
  font-size: 24rpx;
  color: #6C757D;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.profile-modal {
  width: 85%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #E9ECEF;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.modal-content {
  padding: 40rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.label {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 16rpx;
}

.input-field {
  width: 100%;
  padding: 24rpx;
  background-color: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #212529;
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: #2E86AB;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  padding: 16rpx 24rpx;
  background-color: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #495057;
  transition: all 0.3s ease;
}

.tag.selected {
  background-color: #E3F2FD;
  border-color: #2E86AB;
  color: #2E86AB;
}

.tag:active {
  transform: scale(0.95);
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 20rpx 40rpx 40rpx;
  border-top: 1rpx solid #E9ECEF;
}

.cancel-btn,
.save-btn {
  flex: 1;
  padding: 28rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #F8F9FA;
  color: #6C757D;
  border: 2rpx solid #E9ECEF;
}

.save-btn {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
  border: none;
}

.save-btn.disabled {
  background: #E9ECEF;
  color: #6C757D;
}

.cancel-btn:active,
.save-btn:not(.disabled):active {
  transform: scale(0.98);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F3F3F3;
  border-top: 6rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6C757D;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
