// cloudfunctions/updateUserProfile/index.js
// 更新用户资料云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { openid, profile } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    // 验证必填字段
    if (!profile.realName || !profile.phone) {
      return {
        success: false,
        error: '真实姓名和手机号码为必填项'
      }
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(profile.phone)) {
      return {
        success: false,
        error: '手机号码格式不正确'
      }
    }

    // 检查手机号是否已被其他用户使用
    const phoneCheckResult = await db.collection('user_profiles').where({
      phone: profile.phone,
      openid: db.command.neq(currentOpenid)
    }).get()

    if (phoneCheckResult.data.length > 0) {
      return {
        success: false,
        error: '该手机号已被其他用户使用'
      }
    }

    const now = new Date()
    const profileData = {
      openid: currentOpenid,
      realName: profile.realName,
      phone: profile.phone,
      company: profile.company || '',
      position: profile.position || '',
      interests: profile.interests || [],
      updateTime: now
    }

    // 查询是否已有资料记录
    const existingResult = await db.collection('user_profiles').where({
      openid: currentOpenid
    }).get()

    if (existingResult.data.length > 0) {
      // 更新现有记录
      await db.collection('user_profiles').doc(existingResult.data[0]._id).update({
        data: profileData
      })
    } else {
      // 创建新记录
      profileData.createTime = now
      await db.collection('user_profiles').add({
        data: profileData
      })
    }

    // 同时更新用户表中的基本信息
    await db.collection('users').where({
      openid: currentOpenid
    }).update({
      data: {
        realName: profile.realName,
        phone: profile.phone,
        company: profile.company,
        position: profile.position,
        profileCompleted: true,
        updateTime: now
      }
    })

    console.log('用户资料更新成功:', currentOpenid)

    return {
      success: true,
      message: '资料更新成功',
      profile: profileData
    }
  } catch (error) {
    console.error('更新用户资料失败:', error)
    return {
      success: false,
      error: error.message,
      message: '资料更新失败'
    }
  }
}
