<!--pages/subscription/subscription.wxml-->
<view class="subscription-container">
  <!-- 当前订阅状态 -->
  <view class="current-status-section">
    <view class="status-card">
      <view class="status-header">
        <view class="status-title">当前订阅</view>
        <view class="status-badge {{currentSubscription.status}}">
          {{currentSubscription.statusText}}
        </view>
      </view>

      <view class="status-content">
        <view class="plan-name">{{currentSubscription.tierName}}</view>
        <view class="plan-details">
          <view class="detail-item">
            <text class="label">剩余额度：</text>
            <text class="value">{{currentSubscription.quotaRemaining}}/{{currentSubscription.quotaTotal}}</text>
          </view>
          <view class="detail-item" wx:if="{{currentSubscription.expireTime}}">
            <text class="label">到期时间：</text>
            <text class="value">{{currentSubscription.expireTimeText}}</text>
          </view>
        </view>

        <!-- 额度进度条 -->
        <view class="quota-progress">
          <view class="progress-bar">
            <view
              class="progress-fill"
              style="width: {{quotaPercentage}}%"
            ></view>
          </view>
          <view class="progress-text">已使用 {{currentSubscription.quotaUsed}} 次</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 套餐选择 -->
  <view class="plans-section">
    <view class="section-title">选择套餐</view>
    <view class="plans-grid">
      <view 
        class="plan-card {{item.id === selectedPlan ? 'selected' : ''}} {{item.popular ? 'popular' : ''}}"
        wx:for="{{subscriptionPlans}}"
        wx:key="id"
        bindtap="selectPlan"
        data-id="{{item.id}}"
      >
        <view class="popular-badge" wx:if="{{item.popular}}">推荐</view>
        
        <view class="plan-header">
          <view class="plan-name">{{item.name}}</view>
          <view class="plan-price">
            <text class="currency">¥</text>
            <text class="amount">{{item.price}}</text>
            <text class="period">/月</text>
          </view>
        </view>
        
        <view class="plan-features">
          <view class="feature-item">
            <image class="feature-icon" src="/images/check-icon.png" mode="aspectFit"></image>
            <text class="feature-text">{{item.quota}} 次AI对话</text>
          </view>
          
          <view class="feature-item" wx:for="{{item.models}}" wx:for-item="model" wx:key="*this">
            <image class="feature-icon" src="/images/check-icon.png" mode="aspectFit"></image>
            <text class="feature-text">{{modelNames[model]}}</text>
          </view>
          
          <view class="feature-item" wx:if="{{item.features}}">
            <image class="feature-icon" src="/images/check-icon.png" mode="aspectFit"></image>
            <text class="feature-text">专业领域优化</text>
          </view>
          
          <view class="feature-item" wx:if="{{item.priority}}">
            <image class="feature-icon" src="/images/check-icon.png" mode="aspectFit"></image>
            <text class="feature-text">优先响应</text>
          </view>
        </view>
        
        <view class="plan-footer">
          <view class="original-price" wx:if="{{item.originalPrice}}">
            原价 ¥{{item.originalPrice}}
          </view>
          <view class="discount" wx:if="{{item.discount}}">
            限时{{item.discount}}折
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section" wx:if="{{selectedPlan}}">
    <view class="section-title">支付方式</view>
    <view class="payment-methods">
      <view 
        class="payment-method {{paymentMethod === 'wechat' ? 'selected' : ''}}"
        bindtap="selectPaymentMethod"
        data-method="wechat"
      >
        <image class="payment-icon" src="/images/wechat-pay-icon.png" mode="aspectFit"></image>
        <view class="payment-info">
          <view class="payment-name">微信支付</view>
          <view class="payment-desc">安全便捷，即时到账</view>
        </view>
        <view class="payment-radio">
          <view class="radio-dot" wx:if="{{paymentMethod === 'wechat'}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section" wx:if="{{selectedPlan}}">
    <view class="section-title">订单信息</view>
    <view class="order-card">
      <view class="order-item">
        <text class="item-label">套餐名称</text>
        <text class="item-value">{{selectedPlanInfo.name}}</text>
      </view>
      <view class="order-item">
        <text class="item-label">订阅时长</text>
        <text class="item-value">1个月</text>
      </view>
      <view class="order-item">
        <text class="item-label">AI对话次数</text>
        <text class="item-value">{{selectedPlanInfo.quota}}次</text>
      </view>
      <view class="order-item total">
        <text class="item-label">支付金额</text>
        <text class="item-value price">¥{{selectedPlanInfo.price}}</text>
      </view>
    </view>
  </view>

  <!-- 用户协议 -->
  <view class="agreement-section" wx:if="{{selectedPlan}}">
    <view class="agreement-checkbox" bindtap="toggleAgreement">
      <view class="checkbox {{agreedToTerms ? 'checked' : ''}}">
        <image wx:if="{{agreedToTerms}}" class="check-icon" src="/images/check-white-icon.png" mode="aspectFit"></image>
      </view>
      <text class="agreement-text">
        我已阅读并同意
        <text class="link" bindtap="showSubscriptionTerms">《订阅服务协议》</text>
        和
        <text class="link" bindtap="showRefundPolicy">《退款政策》</text>
      </text>
    </view>
  </view>

  <!-- 立即订阅按钮 -->
  <view class="subscribe-section" wx:if="{{selectedPlan}}">
    <button 
      class="subscribe-btn {{!canSubscribe ? 'disabled' : ''}}"
      bindtap="subscribe"
      disabled="{{!canSubscribe}}"
    >
      立即订阅 ¥{{selectedPlanInfo.price}}
    </button>
  </view>

  <!-- 订阅历史 -->
  <view class="history-section" wx:if="{{subscriptionHistory.length > 0}}">
    <view class="section-title">订阅历史</view>
    <view class="history-list">
      <view class="history-item" wx:for="{{subscriptionHistory}}" wx:key="id">
        <view class="history-content">
          <view class="history-plan">{{item.tierName}}</view>
          <view class="history-time">{{item.createTimeText}}</view>
        </view>
        <view class="history-status {{item.status}}">
          {{item.statusText}}
        </view>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="faq-section">
    <view class="section-title">常见问题</view>
    <view class="faq-list">
      <view class="faq-item" wx:for="{{faqList}}" wx:key="id" bindtap="toggleFaq" data-id="{{item.id}}">
        <view class="faq-question">
          <text class="question-text">{{item.question}}</text>
          <image 
            class="expand-icon {{item.expanded ? 'expanded' : ''}}" 
            src="/images/arrow-down-icon.png" 
            mode="aspectFit"
          ></image>
        </view>
        <view class="faq-answer {{item.expanded ? 'show' : ''}}">
          {{item.answer}}
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view>
