/* pages/subscription/subscription.wxss */

.subscription-container {
  padding: 20rpx;
  padding-bottom: 120rpx;
  background-color: #F5F7FA;
  min-height: 100vh;
}

/* 当前订阅状态 */
.current-status-section {
  margin-bottom: 40rpx;
}

.status-card {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  color: #FFFFFF;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-badge.active {
  background-color: #28A745;
}

.status-badge.expired {
  background-color: #DC3545;
}

.status-badge.free {
  background-color: rgba(255, 255, 255, 0.2);
}

.plan-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.plan-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 28rpx;
}

.label {
  opacity: 0.9;
}

.value {
  font-weight: 500;
}

/* 额度进度条 */
.quota-progress {
  margin-top: 24rpx;
}

.progress-bar {
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  opacity: 0.8;
  text-align: center;
}

/* 区域标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 32rpx;
  padding-left: 20rpx;
}

/* 套餐选择 */
.plans-section {
  margin-bottom: 40rpx;
}

.plans-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.plan-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  border: 2rpx solid #E9ECEF;
  position: relative;
  transition: all 0.3s ease;
}

.plan-card.selected {
  border-color: #2E86AB;
  box-shadow: 0 8rpx 24rpx rgba(46, 134, 171, 0.15);
}

.plan-card.popular {
  border-color: #FF6B35;
}

.popular-badge {
  position: absolute;
  top: -12rpx;
  right: 24rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: #FFFFFF;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.plan-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.plan-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16rpx;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.currency {
  font-size: 24rpx;
  color: #6C757D;
}

.amount {
  font-size: 48rpx;
  font-weight: 600;
  color: #2E86AB;
  margin: 0 4rpx;
}

.period {
  font-size: 24rpx;
  color: #6C757D;
}

/* 套餐特性 */
.plan-features {
  margin-bottom: 24rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #495057;
  flex: 1;
}

.plan-footer {
  text-align: center;
  margin-top: 24rpx;
}

.original-price {
  font-size: 24rpx;
  color: #6C757D;
  text-decoration: line-through;
  margin-bottom: 8rpx;
}

.discount {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: 500;
}

/* 支付方式 */
.payment-section {
  margin-bottom: 40rpx;
}

.payment-methods {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F3F4;
  transition: background-color 0.3s ease;
}

.payment-method:last-child {
  border-bottom: none;
}

.payment-method.selected {
  background-color: #F8F9FA;
}

.payment-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.payment-desc {
  font-size: 26rpx;
  color: #6C757D;
}

.payment-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #DEE2E6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-method.selected .payment-radio {
  border-color: #2E86AB;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #2E86AB;
  border-radius: 50%;
}

/* 订单信息 */
.order-section {
  margin-bottom: 40rpx;
}

.order-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F1F3F4;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item.total {
  font-weight: 600;
  font-size: 32rpx;
  margin-top: 16rpx;
  padding-top: 24rpx;
  border-top: 2rpx solid #E9ECEF;
}

.item-label {
  font-size: 28rpx;
  color: #6C757D;
}

.item-value {
  font-size: 28rpx;
  color: #212529;
}

.item-value.price {
  color: #2E86AB;
  font-size: 36rpx;
}

/* 用户协议 */
.agreement-section {
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #DEE2E6;
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.checkbox.checked {
  background-color: #2E86AB;
  border-color: #2E86AB;
}

.check-icon {
  width: 20rpx;
  height: 20rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #6C757D;
  line-height: 1.6;
  flex: 1;
}

.link {
  color: #2E86AB;
  text-decoration: underline;
}

/* 订阅按钮 */
.subscribe-section {
  margin-bottom: 40rpx;
}

.subscribe-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subscribe-btn.disabled {
  background: #E9ECEF;
  color: #ADB5BD;
}

.subscribe-btn:active:not(.disabled) {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 订阅历史 */
.history-section {
  margin-bottom: 40rpx;
}

.history-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F3F4;
}

.history-item:last-child {
  border-bottom: none;
}

.history-content {
  flex: 1;
}

.history-plan {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.history-time {
  font-size: 26rpx;
  color: #6C757D;
}

.history-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.history-status.active {
  background-color: #D4EDDA;
  color: #155724;
}

.history-status.expired {
  background-color: #F8D7DA;
  color: #721C24;
}

/* 常见问题 */
.faq-section {
  margin-bottom: 40rpx;
}

.faq-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.faq-item {
  border-bottom: 1rpx solid #F1F3F4;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  cursor: pointer;
}

.question-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #212529;
  flex: 1;
}

.expand-icon {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 32rpx;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  font-size: 28rpx;
  color: #6C757D;
  line-height: 1.6;
}

.faq-answer.show {
  max-height: 200rpx;
  padding: 0 32rpx 32rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F3F3F3;
  border-top: 6rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6C757D;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
