// cloudfunctions/manageChatSession/index.js
// 聊天会话管理云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, openid, sessionId, sessionData } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    switch (action) {
      case 'create':
        return await createChatSession(currentOpenid, sessionData)
      case 'list':
        return await getChatSessions(currentOpenid, event.limit, event.offset)
      case 'get':
        return await getChatSession(currentOpenid, sessionId)
      case 'update':
        return await updateChatSession(currentOpenid, sessionId, sessionData)
      case 'delete':
        return await deleteChatSession(currentOpenid, sessionId)
      case 'getMessages':
        return await getChatMessages(currentOpenid, sessionId, event.limit, event.offset)
      default:
        return {
          success: false,
          error: '不支持的操作'
        }
    }
  } catch (error) {
    console.error('聊天会话管理失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 创建聊天会话
async function createChatSession(openid, sessionData) {
  const now = new Date()
  
  const session = {
    openid: openid,
    title: sessionData.title || '新对话',
    consultationType: sessionData.consultationType || 'research',
    modelId: sessionData.modelId || 'deepseek-v3',
    modelName: sessionData.modelName || 'DeepSeek-V3',
    messageCount: 0,
    lastMessageTime: now,
    createTime: now,
    updateTime: now,
    status: 'active'
  }

  const result = await db.collection('chat_sessions').add({
    data: session
  })

  return {
    success: true,
    session: {
      ...session,
      _id: result._id
    }
  }
}

// 获取聊天会话列表
async function getChatSessions(openid, limit = 20, offset = 0) {
  const result = await db.collection('chat_sessions')
    .where({
      openid: openid,
      status: 'active'
    })
    .orderBy('lastMessageTime', 'desc')
    .skip(offset)
    .limit(limit)
    .get()

  // 格式化时间
  const sessions = result.data.map(session => ({
    ...session,
    createTimeText: formatTime(session.createTime),
    lastMessageTimeText: formatTime(session.lastMessageTime),
    consultationTypeText: getConsultationTypeText(session.consultationType)
  }))

  return {
    success: true,
    sessions: sessions,
    total: result.data.length
  }
}

// 获取单个聊天会话
async function getChatSession(openid, sessionId) {
  const result = await db.collection('chat_sessions')
    .where({
      _id: sessionId,
      openid: openid
    })
    .get()

  if (result.data.length === 0) {
    return {
      success: false,
      error: '会话不存在'
    }
  }

  const session = result.data[0]
  
  return {
    success: true,
    session: {
      ...session,
      createTimeText: formatTime(session.createTime),
      lastMessageTimeText: formatTime(session.lastMessageTime),
      consultationTypeText: getConsultationTypeText(session.consultationType)
    }
  }
}

// 更新聊天会话
async function updateChatSession(openid, sessionId, updateData) {
  const now = new Date()
  
  const data = {
    updateTime: now
  }

  // 允许更新的字段
  if (updateData.title) data.title = updateData.title
  if (updateData.consultationType) data.consultationType = updateData.consultationType
  if (updateData.modelId) data.modelId = updateData.modelId
  if (updateData.modelName) data.modelName = updateData.modelName
  if (updateData.lastMessageTime) data.lastMessageTime = updateData.lastMessageTime
  if (updateData.messageCount !== undefined) data.messageCount = updateData.messageCount

  const result = await db.collection('chat_sessions')
    .where({
      _id: sessionId,
      openid: openid
    })
    .update({
      data: data
    })

  if (result.stats.updated === 0) {
    return {
      success: false,
      error: '会话不存在或无权限'
    }
  }

  return {
    success: true,
    message: '会话更新成功'
  }
}

// 删除聊天会话
async function deleteChatSession(openid, sessionId) {
  const now = new Date()
  
  // 软删除，只更新状态
  const result = await db.collection('chat_sessions')
    .where({
      _id: sessionId,
      openid: openid
    })
    .update({
      data: {
        status: 'deleted',
        deleteTime: now,
        updateTime: now
      }
    })

  if (result.stats.updated === 0) {
    return {
      success: false,
      error: '会话不存在或无权限'
    }
  }

  // 同时软删除相关的聊天消息
  await db.collection('chat_messages')
    .where({
      sessionId: sessionId,
      openid: openid
    })
    .update({
      data: {
        status: 'deleted',
        deleteTime: now,
        updateTime: now
      }
    })

  return {
    success: true,
    message: '会话删除成功'
  }
}

// 获取聊天消息
async function getChatMessages(openid, sessionId, limit = 50, offset = 0) {
  // 验证会话权限
  const sessionResult = await db.collection('chat_sessions')
    .where({
      _id: sessionId,
      openid: openid,
      status: 'active'
    })
    .get()

  if (sessionResult.data.length === 0) {
    return {
      success: false,
      error: '会话不存在或无权限'
    }
  }

  // 获取消息列表
  const messagesResult = await db.collection('chat_messages')
    .where({
      sessionId: sessionId,
      openid: openid,
      status: db.command.neq('deleted')
    })
    .orderBy('createTime', 'asc')
    .skip(offset)
    .limit(limit)
    .get()

  // 格式化消息
  const messages = messagesResult.data.map(message => ({
    ...message,
    createTimeText: formatTime(message.createTime)
  }))

  return {
    success: true,
    session: sessionResult.data[0],
    messages: messages,
    total: messages.length
  }
}

// 格式化时间
function formatTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  const year = target.getFullYear()
  const month = (target.getMonth() + 1).toString().padStart(2, '0')
  const day = target.getDate().toString().padStart(2, '0')
  const hour = target.getHours().toString().padStart(2, '0')
  const minute = target.getMinutes().toString().padStart(2, '0')
  
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hour}:${minute}`
  } else {
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
}

// 获取咨询类型文本
function getConsultationTypeText(type) {
  const typeMap = {
    'research': '产品研发',
    'registration': '注册申报',
    'sales': '市场销售',
    'quality': '质量管理',
    'regulatory': '法规事务',
    'clinical': '临床试验'
  }
  return typeMap[type] || '通用咨询'
}
