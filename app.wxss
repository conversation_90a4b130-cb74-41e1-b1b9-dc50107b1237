/**app.wxss**/
/* 全局样式 */

/* 重置样式 */
page {
  background-color: #F5F7FA;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

/* 容器样式 */
.container {
  padding: 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.page-container {
  padding: 20rpx;
  background-color: #F5F7FA;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 20rpx;
}

.card-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
}

.btn-primary:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: #F8F9FA;
  color: #2E86AB;
  border: 2rpx solid #2E86AB;
}

.btn-secondary:active {
  background-color: #E9ECEF;
}

.btn-outline {
  background-color: transparent;
  color: #2E86AB;
  border: 2rpx solid #2E86AB;
}

.btn-outline:active {
  background-color: #2E86AB;
  color: #FFFFFF;
}

.btn-disabled {
  background-color: #E9ECEF;
  color: #ADB5BD;
  border: none;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #DEE2E6;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #2E86AB;
  outline: none;
}

.input-field.error {
  border-color: #DC3545;
}

.input-error {
  color: #DC3545;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 列表样式 */
.list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F3F4;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #F8F9FA;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: #212529;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #6C757D;
}

.list-item-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 20rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-primary {
  background-color: #E3F2FD;
  color: #2E86AB;
}

.tag-success {
  background-color: #E8F5E8;
  color: #28A745;
}

.tag-warning {
  background-color: #FFF3CD;
  color: #FFC107;
}

.tag-danger {
  background-color: #F8D7DA;
  color: #DC3545;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #6C757D;
  font-size: 28rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  border: 4rpx solid #F3F3F3;
  border-top: 4rpx solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  color: #6C757D;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mt-30 { margin-top: 60rpx; }

.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.mb-30 { margin-bottom: 60rpx; }

.ml-10 { margin-left: 20rpx; }
.mr-10 { margin-right: 20rpx; }

.p-10 { padding: 20rpx; }
.p-20 { padding: 40rpx; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.hidden { display: none; }
.visible { display: block; }
