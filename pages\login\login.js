// pages/login/login.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    userInfo: null,
    loginTime: '',
    profileCompleted: false,
    profile: {
      realName: '',
      phone: '',
      company: '',
      position: ''
    },
    interestOptions: [
      { id: 'research', name: '产品研发', selected: false },
      { id: 'registration', name: '注册申报', selected: false },
      { id: 'sales', name: '市场销售', selected: false },
      { id: 'quality', name: '质量管理', selected: false },
      { id: 'regulatory', name: '法规事务', selected: false },
      { id: 'clinical', name: '临床试验', selected: false }
    ],
    canSaveProfile: false,
    loading: false,
    loadingText: '登录中...'
  },

  onLoad(options) {
    console.log('登录页面加载')
    this.checkLoginStatus()
  },

  onShow() {
    console.log('登录页面显示')
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        loginTime: util.formatTime(new Date(), 'MM-DD HH:mm')
      })
      this.checkProfileStatus()
    }
  },

  // 检查用户资料完善状态
  async checkProfileStatus() {
    if (!app.globalData.openid) return

    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserProfile',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success && result.result.profile) {
        const profile = result.result.profile
        this.setData({
          profileCompleted: true,
          profile: {
            realName: profile.realName || '',
            phone: profile.phone || '',
            company: profile.company || '',
            position: profile.position || ''
          }
        })

        // 更新兴趣标签
        if (profile.interests && profile.interests.length > 0) {
          const updatedOptions = this.data.interestOptions.map(option => ({
            ...option,
            selected: profile.interests.includes(option.id)
          }))
          this.setData({
            interestOptions: updatedOptions
          })
        }
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
    }
  },

  // 获取用户信息
  async onGetUserProfile(e) {
    console.log('获取用户信息:', e.detail)
    
    if (e.detail.errMsg !== 'getUserProfile:ok') {
      util.showError('授权失败，请重试')
      return
    }

    this.setData({
      loading: true,
      loadingText: '登录中...'
    })

    try {
      // 获取用户信息
      const userInfo = e.detail.userInfo
      
      // 调用云函数获取openid并保存用户信息
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          userInfo: userInfo
        }
      })

      if (result.result.success) {
        // 保存用户信息到全局数据和本地存储
        app.globalData.userInfo = userInfo
        app.globalData.openid = result.result.openid
        
        await util.setStorage('userInfo', userInfo)
        await util.setStorage('openid', result.result.openid)

        this.setData({
          userInfo: userInfo,
          loginTime: util.formatTime(new Date(), 'MM-DD HH:mm'),
          loading: false
        })

        util.showSuccess('登录成功')
        
        // 检查用户资料完善状态
        this.checkProfileStatus()
        
        // 获取用户订阅信息
        app.getUserSubscription()
      } else {
        throw new Error(result.result.error || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      util.showError('登录失败，请重试')
      this.setData({
        loading: false
      })
    }
  },

  // 输入事件处理
  onRealNameInput(e) {
    this.setData({
      'profile.realName': e.detail.value
    })
    this.checkCanSaveProfile()
  },

  onPhoneInput(e) {
    this.setData({
      'profile.phone': e.detail.value
    })
    this.checkCanSaveProfile()
  },

  onCompanyInput(e) {
    this.setData({
      'profile.company': e.detail.value
    })
    this.checkCanSaveProfile()
  },

  onPositionInput(e) {
    this.setData({
      'profile.position': e.detail.value
    })
    this.checkCanSaveProfile()
  },

  // 兴趣标签点击
  onInterestTap(e) {
    const id = e.currentTarget.dataset.id
    const updatedOptions = this.data.interestOptions.map(option => {
      if (option.id === id) {
        return { ...option, selected: !option.selected }
      }
      return option
    })
    
    this.setData({
      interestOptions: updatedOptions
    })
    this.checkCanSaveProfile()
  },

  // 检查是否可以保存资料
  checkCanSaveProfile() {
    const { realName, phone } = this.data.profile
    const hasSelectedInterest = this.data.interestOptions.some(option => option.selected)
    
    this.setData({
      canSaveProfile: realName.trim() !== '' && phone.trim() !== '' && hasSelectedInterest
    })
  },

  // 保存用户资料
  async saveProfile() {
    if (!this.data.canSaveProfile) return

    // 验证手机号
    if (!util.validatePhone(this.data.profile.phone)) {
      util.showError('请输入正确的手机号码')
      return
    }

    this.setData({
      loading: true,
      loadingText: '保存中...'
    })

    try {
      const selectedInterests = this.data.interestOptions
        .filter(option => option.selected)
        .map(option => option.id)

      const profileData = {
        ...this.data.profile,
        interests: selectedInterests,
        updateTime: new Date()
      }

      const result = await wx.cloud.callFunction({
        name: 'updateUserProfile',
        data: {
          openid: app.globalData.openid,
          profile: profileData
        }
      })

      if (result.result.success) {
        this.setData({
          profileCompleted: true,
          loading: false
        })
        util.showSuccess('资料保存成功')
      } else {
        throw new Error(result.result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存用户资料失败:', error)
      util.showError('保存失败，请重试')
      this.setData({
        loading: false
      })
    }
  },

  // 退出登录
  async logout() {
    const confirmed = await util.showConfirm('确定要退出登录吗？')
    if (!confirmed) return

    try {
      // 清除本地存储
      await util.removeStorage('userInfo')
      await util.removeStorage('openid')
      
      // 清除全局数据
      app.globalData.userInfo = null
      app.globalData.openid = null
      app.globalData.subscription = null

      this.setData({
        userInfo: null,
        profileCompleted: false,
        profile: {
          realName: '',
          phone: '',
          company: '',
          position: ''
        }
      })

      util.showSuccess('已退出登录')
    } catch (error) {
      console.error('退出登录失败:', error)
      util.showError('退出失败')
    }
  },

  // 进入应用
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 显示用户协议
  showUserAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/agreement?type=user'
    })
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/agreement?type=privacy'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业AI咨询服务',
      path: '/pages/login/login',
      imageUrl: '/images/share-cover.png'
    }
  }
})
