// app.js
App({
  globalData: {
    userInfo: null,
    openid: null,
    subscription: null,
    currentModel: 'deepseek-v3', // 默认AI模型
    availableModels: [
      { id: 'deepseek-v3', name: 'DeepSeek-V3-0324', tier: 1 },
      { id: 'deepseek-r1', name: 'DeepSeek-R1-0528', tier: 1 },
      { id: 'chatgpt', name: 'ChatGPT', tier: 2 },
      { id: 'gemini', name: '<PERSON>', tier: 3 },
      { id: 'qwen3', name: 'Qwen3', tier: 2 },
      { id: 'qwen-max', name: 'Qwen Max', tier: 4 }
    ],
    subscriptionTiers: [
      { id: 1, name: '基础版', price: 9.9, models: ['deepseek-v3', 'deepseek-r1'], quota: 100 },
      { id: 2, name: '标准版', price: 19.9, models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'qwen3'], quota: 300 },
      { id: 3, name: '专业版', price: 29.9, models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'gemini', 'qwen3'], quota: 500 },
      { id: 4, name: '旗舰版', price: 99.9, models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'gemini', 'qwen3', 'qwen-max'], quota: 2000 }
    ]
  },

  onLaunch() {
    console.log('IVD智能顾问小程序启动')
    
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloudbase-7g8nxwah43c62b19',
        traceUser: true,
      })
    }

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(msg) {
    console.error('小程序错误:', msg)
  },

  // 检查登录状态
  checkLoginStatus() {
    const that = this
    wx.getStorage({
      key: 'userInfo',
      success(res) {
        that.globalData.userInfo = res.data
        that.getUserOpenId()
      },
      fail() {
        console.log('用户未登录')
      }
    })
  },

  // 获取用户OpenID
  getUserOpenId() {
    const that = this
    wx.cloud.callFunction({
      name: 'getUserInfo',
      success: res => {
        console.log('获取OpenID成功:', res.result.openid)
        that.globalData.openid = res.result.openid
        that.getUserSubscription()
      },
      fail: err => {
        console.error('获取OpenID失败:', err)
      }
    })
  },

  // 获取用户订阅信息
  getUserSubscription() {
    const that = this
    if (!that.globalData.openid) return

    wx.cloud.callFunction({
      name: 'getUserSubscription',
      data: {
        openid: that.globalData.openid
      },
      success: res => {
        console.log('获取订阅信息成功:', res.result)
        that.globalData.subscription = res.result.subscription
      },
      fail: err => {
        console.error('获取订阅信息失败:', err)
      }
    })
  },

  // 工具函数：格式化时间
  formatTime(date) {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours()
    const minute = date.getMinutes()
    const second = date.getSeconds()

    return `${[year, month, day].map(this.formatNumber).join('/')} ${[hour, minute, second].map(this.formatNumber).join(':')}`
  },

  formatNumber(n) {
    n = n.toString()
    return n[1] ? n : `0${n}`
  },

  // 检查用户是否有权限使用指定模型
  checkModelPermission(modelId) {
    if (!this.globalData.subscription) {
      return false
    }
    
    const tier = this.globalData.subscription.tier || 1
    const tierInfo = this.globalData.subscriptionTiers.find(t => t.id === tier)
    
    return tierInfo && tierInfo.models.includes(modelId)
  },

  // 检查用户剩余额度
  checkQuotaRemaining() {
    if (!this.globalData.subscription) {
      return 0
    }
    
    return this.globalData.subscription.quotaRemaining || 0
  }
})
