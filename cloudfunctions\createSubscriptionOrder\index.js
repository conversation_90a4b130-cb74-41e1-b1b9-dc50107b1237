// cloudfunctions/createSubscriptionOrder/index.js
// 创建订阅订单云函数

const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 订阅套餐配置
const SUBSCRIPTION_PLANS = {
  1: { name: '基础版', price: 9.9, quota: 100, tier: 1 },
  2: { name: '标准版', price: 19.9, quota: 300, tier: 2 },
  3: { name: '专业版', price: 29.9, quota: 500, tier: 3 },
  4: { name: '旗舰版', price: 99.9, quota: 2000, tier: 4 }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { openid, planId, paymentMethod } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    // 验证套餐
    const plan = SUBSCRIPTION_PLANS[planId]
    if (!plan) {
      return {
        success: false,
        error: '无效的订阅套餐'
      }
    }

    // 检查用户是否已有有效订阅
    const existingSubscription = await db.collection('subscriptions').where({
      openid: currentOpenid,
      status: 'active'
    }).get()

    if (existingSubscription.data.length > 0) {
      const sub = existingSubscription.data[0]
      if (sub.expireTime && new Date(sub.expireTime) > new Date()) {
        return {
          success: false,
          error: '您已有有效订阅，请等待到期后再购买'
        }
      }
    }

    // 生成订单号
    const outTradeNo = generateOrderNo()
    const now = new Date()
    const expireTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后过期

    // 创建订单记录
    const orderData = {
      openid: currentOpenid,
      outTradeNo: outTradeNo,
      planId: planId,
      planName: plan.name,
      amount: plan.price,
      quota: plan.quota,
      tier: plan.tier,
      paymentMethod: paymentMethod,
      status: 'pending',
      createTime: now,
      expireTime: expireTime
    }

    const orderResult = await db.collection('payment_orders').add({
      data: orderData
    })

    console.log('订单创建成功:', outTradeNo)

    // 调用微信支付统一下单
    const paymentResult = await createWechatPayOrder({
      openid: currentOpenid,
      outTradeNo: outTradeNo,
      body: `IVD智能顾问-${plan.name}`,
      totalFee: Math.round(plan.price * 100), // 转换为分
      notifyUrl: getPaymentNotifyUrl()
    })

    if (!paymentResult.success) {
      // 更新订单状态为失败
      await db.collection('payment_orders').doc(orderResult._id).update({
        data: {
          status: 'failed',
          errorMessage: paymentResult.error,
          updateTime: new Date()
        }
      })

      return {
        success: false,
        error: paymentResult.error
      }
    }

    // 更新订单支付信息
    await db.collection('payment_orders').doc(orderResult._id).update({
      data: {
        prepayId: paymentResult.prepayId,
        updateTime: new Date()
      }
    })

    return {
      success: true,
      order: {
        orderId: orderResult._id,
        outTradeNo: outTradeNo,
        timeStamp: paymentResult.timeStamp,
        nonceStr: paymentResult.nonceStr,
        package: paymentResult.package,
        signType: paymentResult.signType,
        paySign: paymentResult.paySign
      }
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 生成订单号
function generateOrderNo() {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `IVD${timestamp}${random}`
}

// 创建微信支付订单
async function createWechatPayOrder({ openid, outTradeNo, body, totalFee, notifyUrl }) {
  try {
    // 这里需要调用微信支付API
    // 由于需要商户证书等敏感信息，实际实现时需要使用环境变量
    
    const appid = process.env.WX_APPID || 'wx40de5ae4b1c122b6'
    const mchid = process.env.WX_PAY_MCHID
    const key = process.env.WX_PAY_KEY
    
    if (!mchid || !key) {
      throw new Error('微信支付配置不完整')
    }

    // 统一下单参数
    const unifiedOrderParams = {
      appid: appid,
      mch_id: mchid,
      nonce_str: generateNonceStr(),
      body: body,
      out_trade_no: outTradeNo,
      total_fee: totalFee,
      spbill_create_ip: '127.0.0.1',
      notify_url: notifyUrl,
      trade_type: 'JSAPI',
      openid: openid
    }

    // 生成签名
    const sign = generateWechatPaySign(unifiedOrderParams, key)
    unifiedOrderParams.sign = sign

    // 调用微信支付统一下单API
    // 这里简化处理，实际需要发送HTTP请求到微信支付API
    console.log('微信支付统一下单参数:', unifiedOrderParams)

    // 模拟返回结果（实际开发中需要真实调用微信API）
    const prepayId = `wx${Date.now()}${Math.random().toString(36).substr(2, 9)}`
    
    // 生成小程序支付参数
    const timeStamp = Math.floor(Date.now() / 1000).toString()
    const nonceStr = generateNonceStr()
    const packageStr = `prepay_id=${prepayId}`
    
    const paySignParams = {
      appId: appid,
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageStr,
      signType: 'MD5'
    }
    
    const paySign = generateWechatPaySign(paySignParams, key)

    return {
      success: true,
      prepayId: prepayId,
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageStr,
      signType: 'MD5',
      paySign: paySign
    }
  } catch (error) {
    console.error('微信支付下单失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 生成随机字符串
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15)
}

// 生成微信支付签名
function generateWechatPaySign(params, key) {
  const sortedKeys = Object.keys(params).sort()
  const stringA = sortedKeys.map(k => `${k}=${params[k]}`).join('&')
  const stringSignTemp = `${stringA}&key=${key}`
  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase()
}

// 获取支付回调地址
function getPaymentNotifyUrl() {
  // 实际部署时需要配置真实的回调地址
  return process.env.WX_PAY_NOTIFY_URL || 'https://your-domain.com/pay/notify'
}
