// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    subscriptionText: '未订阅',
    stats: {
      totalChats: 0,
      quotaRemaining: 0,
      favoriteModel: 'DeepSeek-V3'
    }
  },

  onLoad() {
    console.log('首页加载')
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    this.refreshUserInfo()
  },

  // 初始化页面
  initPage() {
    this.refreshUserInfo()
    this.loadUserStats()
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = app.globalData.userInfo
    const subscription = app.globalData.subscription

    this.setData({
      userInfo: userInfo
    })

    if (subscription) {
      const tier = app.globalData.subscriptionTiers.find(t => t.id === subscription.tier)
      this.setData({
        subscriptionText: tier ? tier.name : '未订阅'
      })
    }
  },

  // 加载用户统计数据
  loadUserStats() {
    if (!app.globalData.openid) return

    wx.cloud.callFunction({
      name: 'getUserStats',
      data: {
        openid: app.globalData.openid
      },
      success: res => {
        console.log('获取用户统计成功:', res.result)
        this.setData({
          stats: res.result.stats || this.data.stats
        })
      },
      fail: err => {
        console.error('获取用户统计失败:', err)
      }
    })
  },

  // 开始聊天
  startChat() {
    if (!app.globalData.userInfo) {
      this.showLoginPrompt()
      return
    }

    wx.switchTab({
      url: '/pages/chat/chat'
    })
  },

  // 查看AI模型
  viewModels() {
    if (!app.globalData.userInfo) {
      this.showLoginPrompt()
      return
    }

    wx.showActionSheet({
      itemList: app.globalData.availableModels.map(model => model.name),
      success: res => {
        const selectedModel = app.globalData.availableModels[res.tapIndex]
        
        // 检查用户权限
        if (!app.checkModelPermission(selectedModel.id)) {
          wx.showModal({
            title: '权限不足',
            content: `使用 ${selectedModel.name} 需要升级订阅套餐`,
            confirmText: '立即升级',
            success: modalRes => {
              if (modalRes.confirm) {
                this.viewSubscription()
              }
            }
          })
          return
        }

        // 切换模型
        app.globalData.currentModel = selectedModel.id
        wx.showToast({
          title: `已切换到 ${selectedModel.name}`,
          icon: 'success'
        })
      }
    })
  },

  // 查看订阅套餐
  viewSubscription() {
    wx.navigateTo({
      url: '/pages/subscription/subscription'
    })
  },

  // 研发咨询
  consultResearch() {
    this.startConsultation('research', '产品研发咨询')
  },

  // 注册咨询
  consultRegistration() {
    this.startConsultation('registration', '注册申报咨询')
  },

  // 销售咨询
  consultSales() {
    this.startConsultation('sales', '市场销售咨询')
  },

  // 综合咨询
  consultGeneral() {
    this.startConsultation('general', '综合咨询')
  },

  // 开始专业咨询
  startConsultation(type, title) {
    if (!app.globalData.userInfo) {
      this.showLoginPrompt()
      return
    }

    // 检查剩余额度
    if (app.checkQuotaRemaining() <= 0) {
      wx.showModal({
        title: '额度不足',
        content: '您的咨询额度已用完，请升级订阅套餐',
        confirmText: '立即升级',
        success: res => {
          if (res.confirm) {
            this.viewSubscription()
          }
        }
      })
      return
    }

    // 跳转到聊天页面并传递咨询类型
    wx.switchTab({
      url: `/pages/chat/chat?type=${type}&title=${encodeURIComponent(title)}`
    })
  },

  // 前往登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后使用智能咨询功能',
      confirmText: '立即登录',
      success: res => {
        if (res.confirm) {
          this.goToLogin()
        }
      }
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属研发、注册、销售顾问',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    }
  },

  onShareTimeline() {
    return {
      title: 'IVD智能顾问 - 专业AI咨询服务',
      imageUrl: '/images/share-cover.png'
    }
  }
})
