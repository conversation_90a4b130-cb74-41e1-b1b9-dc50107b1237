<!--pages/chat/chat.wxml-->
<view class="chat-container">
  <!-- 顶部工具栏 -->
  <view class="chat-header">
    <view class="header-left">
      <view class="session-info">
        <view class="session-title">{{currentSession.title}}</view>
        <view class="session-meta">
          <text class="consultation-type">{{currentSession.consultationTypeText}}</text>
          <text class="separator">·</text>
          <text class="model-name">{{currentSession.modelName}}</text>
        </view>
      </view>
    </view>
    
    <view class="header-right">
      <view class="quota-info">
        <text class="quota-text">剩余 {{remainingQuota}} 次</text>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="showModelSelector">
          <image class="action-icon" src="/images/model-icon.png" mode="aspectFit"></image>
        </view>
        <view class="action-btn" bindtap="showSessionMenu">
          <image class="action-icon" src="/images/menu-icon.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 聊天消息区域 -->
  <scroll-view 
    class="chat-messages" 
    scroll-y="true" 
    scroll-top="{{scrollTop}}"
    scroll-into-view="{{scrollIntoView}}"
    enable-back-to-top="true"
  >
    <!-- 欢迎消息 -->
    <view class="welcome-message" wx:if="{{messages.length === 0}}">
      <view class="welcome-avatar">
        <image class="avatar-img" src="/images/ai-avatar.png" mode="aspectFit"></image>
      </view>
      <view class="welcome-content">
        <view class="welcome-title">您好！我是IVD智能顾问</view>
        <view class="welcome-desc">
          我是专业的体外诊断行业AI助手，可以为您提供产品研发、注册申报、市场销售等方面的专业咨询服务。
        </view>
        <view class="quick-questions">
          <view class="quick-title">您可以问我：</view>
          <view
            class="quick-item"
            wx:for="{{quickQuestions}}"
            wx:key="id"
            bindtap="sendQuickQuestion"
            data-question="{{item.question}}"
            data-index="{{index}}"
          >
            <view class="question-text">{{item.question}}</view>
            <view class="question-type">{{item.type}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <view 
        class="message-item {{item.role}}" 
        wx:for="{{messages}}" 
        wx:key="id"
        id="message-{{index}}"
      >
        <!-- 用户消息 -->
        <view class="message-user" wx:if="{{item.role === 'user'}}">
          <view class="message-content">
            <view class="message-text">
              <rich-text nodes="{{item.formattedContent || item.content}}"></rich-text>
            </view>
            <view class="message-time">{{item.timeText}}</view>
          </view>
          <view class="message-avatar">
            <image class="avatar-img" src="{{userInfo.avatarUrl}}" mode="aspectFit"></image>
          </view>
        </view>

        <!-- AI消息 -->
        <view class="message-ai" wx:if="{{item.role === 'assistant'}}">
          <view class="message-avatar">
            <image class="avatar-img" src="/images/ai-avatar.png" mode="aspectFit"></image>
          </view>
          <view class="message-content">
            <view class="message-header">
              <view class="ai-name">{{item.modelName}}</view>
              <view class="message-actions">
                <view class="action-item" bindtap="copyMessage" data-content="{{item.content}}">
                  <image class="action-icon" src="/images/copy-icon.png" mode="aspectFit"></image>
                  <text class="action-text">复制</text>
                </view>
                <view class="action-item" bindtap="likeMessage" data-id="{{item.id}}">
                  <image class="action-icon" src="/images/{{item.liked ? 'like-filled' : 'like'}}-icon.png" mode="aspectFit"></image>
                  <text class="action-text">{{item.liked ? '已赞' : '点赞'}}</text>
                </view>
              </view>
            </view>
            <view class="message-text">
              <text class="text-content">{{item.content}}</text>
            </view>
            <view class="message-time">{{item.timeText}}</view>
          </view>
        </view>
      </view>

      <!-- 正在输入指示器 -->
      <view class="typing-indicator" wx:if="{{isTyping}}">
        <view class="message-avatar">
          <image class="avatar-img" src="/images/ai-avatar.png" mode="aspectFit"></image>
        </view>
        <view class="typing-content">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <view class="typing-text">AI正在思考中...</view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="chat-input-area">
    <!-- 咨询类型选择 -->
    <view class="consultation-selector" wx:if="{{showConsultationSelector}}">
      <view class="selector-title">选择咨询类型</view>
      <view class="consultation-options">
        <view
          class="option-item {{item.id === selectedConsultationType ? 'selected' : ''}}"
          wx:for="{{consultationTypes}}"
          wx:key="id"
          bindtap="selectConsultationType"
          data-type="{{item.id}}"
        >
          <image class="option-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <view class="option-content">
            <view class="option-text">{{item.name}}</view>
            <view class="option-desc">{{item.desc}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 输入框 -->
    <view class="input-container">
      <view class="input-wrapper">
        <textarea 
          class="message-input"
          placeholder="{{inputPlaceholder}}"
          value="{{inputText}}"
          bindinput="onInputChange"
          bindconfirm="sendMessage"
          confirm-type="send"
          auto-height="true"
          maxlength="2000"
          show-confirm-bar="false"
          cursor-spacing="20"
        ></textarea>
        
        <view class="input-actions">
          <view class="action-item" bindtap="toggleConsultationSelector">
            <image class="action-icon" src="/images/consultation-icon.png" mode="aspectFit"></image>
          </view>
          <view class="action-item" bindtap="showVoiceInput">
            <image class="action-icon" src="/images/voice-icon.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
      
      <button 
        class="send-btn {{canSend ? 'active' : ''}}"
        bindtap="sendMessage"
        disabled="{{!canSend || isTyping}}"
      >
        <image class="send-icon" src="/images/send-icon.png" mode="aspectFit"></image>
      </button>
    </view>
  </view>

  <!-- 模型选择器 -->
  <view class="modal-overlay" wx:if="{{showModelModal}}" bindtap="hideModelSelector">
    <view class="model-selector" catchtap="">
      <view class="selector-header">
        <view class="selector-title">选择AI模型</view>
        <view class="close-btn" bindtap="hideModelSelector">
          <image class="close-icon" src="/images/close-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="model-list">
        <view 
          class="model-item {{item.id === currentModel ? 'selected' : ''}} {{!item.available ? 'disabled' : ''}}"
          wx:for="{{availableModels}}"
          wx:key="id"
          bindtap="selectModel"
          data-model="{{item.id}}"
        >
          <view class="model-info">
            <view class="model-name">{{item.name}}</view>
            <view class="model-desc">{{item.description}}</view>
            <view class="model-tier" wx:if="{{!item.available}}">
              需要{{item.tierName}}及以上订阅
            </view>
          </view>
          <view class="model-status">
            <view class="status-dot {{item.available ? 'available' : 'unavailable'}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 会话菜单 -->
  <view class="modal-overlay" wx:if="{{showSessionModal}}" bindtap="hideSessionMenu">
    <view class="session-menu" catchtap="">
      <view class="menu-item" bindtap="newSession">
        <image class="menu-icon" src="/images/new-chat-icon.png" mode="aspectFit"></image>
        <text class="menu-text">新建对话</text>
      </view>
      <view class="menu-item" bindtap="renameSession">
        <image class="menu-icon" src="/images/edit-icon.png" mode="aspectFit"></image>
        <text class="menu-text">重命名</text>
      </view>
      <view class="menu-item" bindtap="exportSession">
        <image class="menu-icon" src="/images/export-icon.png" mode="aspectFit"></image>
        <text class="menu-text">导出对话</text>
      </view>
      <view class="menu-item" bindtap="clearSession">
        <image class="menu-icon" src="/images/clear-icon.png" mode="aspectFit"></image>
        <text class="menu-text">清空对话</text>
      </view>
      <view class="menu-item danger" bindtap="deleteSession">
        <image class="menu-icon" src="/images/delete-icon.png" mode="aspectFit"></image>
        <text class="menu-text">删除对话</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view>
