# IVD智能顾问 2.0.0

一个专业的IVD（体外诊断）行业AI咨询微信小程序，集成多个AI大模型，为研发、注册、销售等专业领域提供智能咨询服务。

## 🌟 功能特性

### 核心功能
- 🤖 **多AI模型集成**: DeepSeek-V3、DeepSeek-R1、ChatGPT、Gemini、Qwen等
- 🎯 **专业咨询领域**: 产品研发、注册申报、销售支持、质量管理、法规事务、临床应用
- 💡 **智能知识库**: IVD行业专业知识和术语解释
- 💬 **实时对话**: 流畅的AI对话体验，支持会话管理
- 📊 **使用统计**: 详细的使用记录和数据分析

### 商业功能
- 💰 **分级订阅系统**: 9.9、19.9、29.9、99.9元多档套餐
- 🔐 **微信一键登录**: 便捷的用户认证体验
- 💳 **微信支付集成**: 安全可靠的支付流程
- 👤 **用户管理**: 完整的用户资料和订阅管理

### 技术特性
- 📱 **原生小程序**: 基于微信小程序原生框架开发
- ☁️ **云原生架构**: 腾讯云开发全托管后端服务
- 🚀 **高性能**: 优化的响应速度和用户体验
- 🔒 **安全可靠**: 完善的数据安全和隐私保护

## 🛠 技术栈

### 前端技术
- **框架**: 微信小程序原生框架
- **基础库**: 3.8.9+
- **UI组件**: 自定义组件库
- **状态管理**: 页面级状态管理

### 后端技术
- **云服务**: 腾讯云开发 (CloudBase)
- **运行时**: Node.js 14+
- **数据库**: 云数据库 MongoDB
- **存储**: 云存储服务
- **函数**: 云函数 Serverless

### 第三方集成
- **支付**: 微信支付 API
- **AI模型**: 
  - DeepSeek API
  - OpenAI API
  - Google Gemini API
  - 阿里云通义千问 API

## 🚀 快速开始

### 环境准备
1. **微信开发者工具**: 下载并安装最新版本
2. **小程序账号**: 注册微信小程序账号
3. **腾讯云账号**: 开通云开发服务
4. **AI API密钥**: 申请各AI平台的API密钥

### 项目配置
1. **克隆项目**
```bash
git clone [repository-url]
cd ivd-ai-advisor
```

2. **配置云开发环境**
```javascript
// app.js
wx.cloud.init({
  env: 'your-cloud-env-id',
  traceUser: true
})
```

3. **设置环境变量**
在云开发控制台配置：
```
DEEPSEEK_API_KEY=your_deepseek_key
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
QWEN_API_KEY=your_qwen_key
WECHAT_PAY_MCHID=your_merchant_id
WECHAT_PAY_KEY=your_pay_key
```

4. **部署云函数**
- 在微信开发者工具中右键云函数目录
- 选择"上传并部署：云端安装依赖"

5. **配置小程序**
- 设置AppID和服务器域名
- 配置支付参数

### 本地开发
1. 在微信开发者工具中打开项目
2. 选择云开发环境
3. 编译并预览

## 📁 项目结构

```
ivd-ai-advisor/
├── pages/                      # 小程序页面
│   ├── index/                  # 首页
│   ├── chat/                   # 聊天页面
│   ├── profile/                # 个人中心
│   ├── subscription/           # 订阅管理
│   └── login/                  # 登录页面
├── components/                 # 自定义组件
│   ├── chat-bubble/           # 聊天气泡
│   ├── model-selector/        # 模型选择器
│   └── subscription-card/     # 订阅卡片
├── cloudfunctions/            # 云函数
│   ├── login/                 # 用户登录
│   ├── callAIModel/          # AI模型调用
│   ├── manageChatSession/    # 会话管理
│   ├── getUserSubscription/  # 订阅管理
│   ├── createPaymentOrder/   # 支付订单
│   ├── getIVDKnowledge/     # 知识库
│   └── explainIVDTerm/      # 术语解释
├── utils/                     # 工具函数
├── images/                    # 图片资源
├── docs/                      # 项目文档
├── test/                      # 测试文件
├── app.js                     # 小程序入口
├── app.json                   # 小程序配置
├── app.wxss                   # 全局样式
└── README.md                  # 项目说明
```

## 📖 文档指南

- [开发指南](docs/development-guide.md) - 详细的开发文档和API说明
- [部署指南](docs/deployment-guide.md) - 完整的部署流程和配置说明
- [测试计划](docs/testing-plan.md) - 测试用例和质量保证

## 🧪 测试

### 功能测试
```javascript
// 在微信开发者工具控制台运行
testCloudFunctions()
```

### 性能测试
```javascript
// 测试AI响应性能
performanceTest()
```

### 数据库测试
```javascript
// 测试数据库连接
databaseTest()
```

## 🚀 部署

### 开发环境
1. 配置测试环境变量
2. 部署云函数到测试环境
3. 在开发者工具中测试

### 生产环境
1. 配置生产环境变量
2. 部署云函数到生产环境
3. 提交小程序审核
4. 发布上线

详细部署步骤请参考 [部署指南](docs/deployment-guide.md)

## 🔧 配置说明

### 订阅套餐配置
```javascript
const SUBSCRIPTION_PLANS = [
  { id: 'basic', name: '基础版', price: 9.9, quota: 100 },
  { id: 'standard', name: '标准版', price: 19.9, quota: 300 },
  { id: 'professional', name: '专业版', price: 29.9, quota: 600 },
  { id: 'enterprise', name: '企业版', price: 99.9, quota: 2000 }
]
```

### AI模型配置
```javascript
const AI_MODELS = {
  'deepseek-chat': { name: 'DeepSeek-V3', tier: 'standard' },
  'deepseek-reasoner': { name: 'DeepSeek-R1', tier: 'professional' },
  'gpt-4': { name: 'ChatGPT-4', tier: 'professional' },
  'gemini-pro': { name: 'Gemini Pro', tier: 'enterprise' }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/your-username/ivd-ai-advisor](https://github.com/your-username/ivd-ai-advisor)

## 🙏 致谢

感谢以下开源项目和服务提供商：
- 微信小程序平台
- 腾讯云开发
- DeepSeek AI
- OpenAI
- Google AI
- 阿里云
