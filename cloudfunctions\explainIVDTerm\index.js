// cloudfunctions/explainIVDTerm/index.js
// IVD专业术语解释云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// IVD专业术语词典
const IVD_TERMS = {
  // 技术术语
  'ELISA': {
    fullName: 'Enzyme-Linked Immunosorbent Assay',
    chinese: '酶联免疫吸附试验',
    definition: '一种基于酶标记的免疫检测技术，通过酶催化底物产生可检测信号来定量检测目标分析物',
    applications: ['蛋白质检测', '抗体检测', '激素检测', '细胞因子检测'],
    advantages: ['特异性强', '灵敏度高', '操作简便', '成本适中'],
    category: 'technology'
  },
  'PCR': {
    fullName: 'Polymerase Chain Reaction',
    chinese: '聚合酶链式反应',
    definition: '一种体外扩增特定DNA序列的技术，通过温度循环实现DNA的变性、退火和延伸',
    applications: ['病原体检测', '基因突变检测', '基因表达分析', '法医鉴定'],
    advantages: ['特异性极强', '灵敏度极高', '快速检测', '可定量分析'],
    category: 'technology'
  },
  'qPCR': {
    fullName: 'Quantitative PCR',
    chinese: '实时荧光定量PCR',
    definition: '在PCR扩增过程中实时监测产物生成的技术，可实现定量检测',
    applications: ['病毒载量检测', '基因表达定量', '拷贝数变异检测', '食品安全检测'],
    advantages: ['实时监测', '定量准确', '污染风险低', '高通量检测'],
    category: 'technology'
  },
  'CLIA': {
    fullName: 'Chemiluminescent Immunoassay',
    chinese: '化学发光免疫分析',
    definition: '基于化学发光反应的免疫检测技术，具有高灵敏度和宽检测范围',
    applications: ['肿瘤标志物', '心脏标志物', '甲状腺功能', '生殖激素'],
    advantages: ['灵敏度极高', '线性范围宽', '自动化程度高', '检测速度快'],
    category: 'technology'
  },
  'POCT': {
    fullName: 'Point-of-Care Testing',
    chinese: '即时检验',
    definition: '在患者身边进行的快速检测，能够立即获得结果并指导临床决策',
    applications: ['血糖检测', '心肌标志物', '感染标志物', '凝血功能'],
    advantages: ['快速出结果', '操作简便', '便携性好', '提高诊疗效率'],
    category: 'technology'
  },

  // 法规术语
  'NMPA': {
    fullName: 'National Medical Products Administration',
    chinese: '国家药品监督管理局',
    definition: '中国负责药品、医疗器械、化妆品监督管理的国家机构',
    responsibilities: ['产品注册审批', '质量监督检查', '不良事件监测', '法规制定'],
    category: 'regulation'
  },
  'FDA': {
    fullName: 'Food and Drug Administration',
    chinese: '美国食品药品监督管理局',
    definition: '美国负责食品、药品、医疗器械等产品安全监管的联邦机构',
    pathways: ['510(k)', 'PMA', 'De Novo', 'HDE'],
    category: 'regulation'
  },
  'CE': {
    fullName: 'Conformité Européenne',
    chinese: '欧洲合格认证',
    definition: '欧盟对产品符合相关指令要求的强制性认证标志',
    directive: 'IVDR 2017/746',
    classes: ['Class A', 'Class B', 'Class C', 'Class D'],
    category: 'regulation'
  },
  'GMP': {
    fullName: 'Good Manufacturing Practice',
    chinese: '良好生产规范',
    definition: '确保产品质量和安全的生产质量管理规范',
    principles: ['人员管理', '厂房设施', '设备管理', '物料管理', '生产管理', '质量控制'],
    category: 'regulation'
  },
  'ISO13485': {
    fullName: 'Medical devices — Quality management systems',
    chinese: '医疗器械质量管理体系',
    definition: '专门针对医疗器械行业的质量管理体系标准',
    requirements: ['质量管理体系', '管理职责', '资源管理', '产品实现', '测量分析改进'],
    category: 'regulation'
  },

  // 临床术语
  'Sensitivity': {
    chinese: '敏感性/灵敏度',
    definition: '检测方法正确识别阳性样本的能力，即真阳性率',
    formula: '敏感性 = 真阳性 / (真阳性 + 假阴性)',
    importance: '高敏感性意味着漏检率低，适用于筛查试验',
    category: 'clinical'
  },
  'Specificity': {
    chinese: '特异性',
    definition: '检测方法正确识别阴性样本的能力，即真阴性率',
    formula: '特异性 = 真阴性 / (真阴性 + 假阳性)',
    importance: '高特异性意味着误诊率低，适用于确诊试验',
    category: 'clinical'
  },
  'PPV': {
    fullName: 'Positive Predictive Value',
    chinese: '阳性预测值',
    definition: '检测结果为阳性时，患者确实患病的概率',
    formula: 'PPV = 真阳性 / (真阳性 + 假阳性)',
    factors: ['疾病患病率', '检测敏感性', '检测特异性'],
    category: 'clinical'
  },
  'NPV': {
    fullName: 'Negative Predictive Value',
    chinese: '阴性预测值',
    definition: '检测结果为阴性时，患者确实未患病的概率',
    formula: 'NPV = 真阴性 / (真阴性 + 假阴性)',
    factors: ['疾病患病率', '检测敏感性', '检测特异性'],
    category: 'clinical'
  },
  'ROC': {
    fullName: 'Receiver Operating Characteristic',
    chinese: '受试者工作特征曲线',
    definition: '评价诊断试验准确性的统计方法，以敏感性为纵坐标，1-特异性为横坐标绘制的曲线',
    applications: ['确定最佳切点', '比较不同方法', '评价诊断价值'],
    category: 'clinical'
  },

  // 质量术语
  'CAPA': {
    fullName: 'Corrective and Preventive Action',
    chinese: '纠正和预防措施',
    definition: '针对已发生或潜在问题采取的纠正和预防措施系统',
    process: ['问题识别', '根本原因分析', '措施制定', '实施验证', '效果评价'],
    category: 'quality'
  },
  'FMEA': {
    fullName: 'Failure Mode and Effects Analysis',
    chinese: '失效模式与影响分析',
    definition: '系统性分析产品或过程潜在失效模式及其影响的风险评估方法',
    steps: ['功能分析', '失效模式识别', '影响分析', '风险评价', '改进措施'],
    category: 'quality'
  },
  'Validation': {
    chinese: '验证',
    definition: '通过提供客观证据对特定的预期用途或应用要求已得到满足的认定',
    types: ['设计验证', '过程验证', '软件验证', '清洁验证'],
    category: 'quality'
  },
  'Verification': {
    chinese: '确认',
    definition: '通过提供客观证据对规定要求已得到满足的认定',
    difference: '验证关注"做正确的事"，确认关注"正确地做事"',
    category: 'quality'
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { term, category } = event
  
  try {
    console.log('查询IVD术语:', { term, category })
    
    if (!term) {
      return {
        success: false,
        error: '请提供要查询的术语'
      }
    }
    
    // 精确匹配
    let result = IVD_TERMS[term.toUpperCase()]
    
    // 如果没有精确匹配，尝试模糊搜索
    if (!result) {
      result = fuzzySearch(term)
    }
    
    if (!result) {
      return {
        success: false,
        error: `未找到术语"${term}"的解释`,
        suggestions: getSuggestions(term)
      }
    }
    
    return {
      success: true,
      term: term,
      explanation: result,
      relatedTerms: getRelatedTerms(result.category),
      timestamp: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('术语解释失败:', error)
    return {
      success: false,
      error: error.message || '术语查询失败'
    }
  }
}

// 模糊搜索
function fuzzySearch(term) {
  const termLower = term.toLowerCase()
  
  for (const [key, value] of Object.entries(IVD_TERMS)) {
    // 检查英文全称
    if (value.fullName && value.fullName.toLowerCase().includes(termLower)) {
      return value
    }
    // 检查中文名称
    if (value.chinese && value.chinese.includes(term)) {
      return value
    }
    // 检查定义
    if (value.definition && value.definition.includes(term)) {
      return value
    }
  }
  
  return null
}

// 获取建议
function getSuggestions(term) {
  const suggestions = []
  const termLower = term.toLowerCase()
  
  for (const [key, value] of Object.entries(IVD_TERMS)) {
    if (key.toLowerCase().includes(termLower) || 
        (value.chinese && value.chinese.includes(term))) {
      suggestions.push({
        term: key,
        chinese: value.chinese,
        category: value.category
      })
    }
  }
  
  return suggestions.slice(0, 5)
}

// 获取相关术语
function getRelatedTerms(category) {
  const related = []
  
  for (const [key, value] of Object.entries(IVD_TERMS)) {
    if (value.category === category) {
      related.push({
        term: key,
        chinese: value.chinese,
        definition: value.definition.substring(0, 100) + '...'
      })
    }
  }
  
  return related.slice(0, 5)
}
