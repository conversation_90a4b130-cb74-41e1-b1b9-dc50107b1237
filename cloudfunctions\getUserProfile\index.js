// cloudfunctions/getUserProfile/index.js
// 获取用户资料云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { openid } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    // 查询用户资料
    const profileResult = await db.collection('user_profiles').where({
      openid: currentOpenid
    }).get()

    if (profileResult.data.length > 0) {
      const profile = profileResult.data[0]
      
      return {
        success: true,
        profile: {
          realName: profile.realName,
          phone: profile.phone,
          company: profile.company,
          position: profile.position,
          interests: profile.interests || [],
          createTime: profile.createTime,
          updateTime: profile.updateTime
        }
      }
    } else {
      return {
        success: true,
        profile: null,
        message: '用户资料不存在'
      }
    }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    return {
      success: false,
      error: error.message,
      profile: null
    }
  }
}
