// pages/chat/chat.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    currentSession: {
      _id: null,
      title: '新对话',
      consultationType: 'research',
      consultationTypeText: '产品研发',
      modelId: 'deepseek-v3',
      modelName: 'DeepSeek-V3'
    },
    messages: [],
    inputText: '',
    inputPlaceholder: '请输入您的问题...',
    canSend: false,
    isTyping: false,
    scrollTop: 0,
    scrollIntoView: '',
    remainingQuota: 0,
    userInfo: null,
    
    // 模型相关
    currentModel: 'deepseek-v3',
    availableModels: [],
    showModelModal: false,
    
    // 咨询类型
    selectedConsultationType: 'research',
    showConsultationSelector: false,
    consultationTypes: [
      { id: 'research', name: '产品研发', icon: '/images/research-icon.png', desc: '技术路线、产品设计、研发管理' },
      { id: 'registration', name: '注册申报', icon: '/images/registration-icon.png', desc: 'NMPA注册、FDA认证、CE标识' },
      { id: 'sales', name: '市场销售', icon: '/images/sales-icon.png', desc: '销售策略、渠道建设、客户管理' },
      { id: 'quality', name: '质量管理', icon: '/images/quality-icon.png', desc: 'ISO13485、质量体系、风险管理' },
      { id: 'regulatory', name: '法规事务', icon: '/images/regulatory-icon.png', desc: '法规解读、政策分析、合规策略' },
      { id: 'clinical', name: '临床试验', icon: '/images/clinical-icon.png', desc: '试验设计、统计分析、GCP管理' }
    ],
    
    // 快速问题
    quickQuestions: [
      { id: 1, question: '如何进行IVD产品的技术路线选择？', type: 'research' },
      { id: 2, question: 'NMPA注册申报需要准备哪些材料？', type: 'registration' },
      { id: 3, question: 'IVD产品的市场推广策略有哪些？', type: 'sales' },
      { id: 4, question: '临床试验设计的关键要素是什么？', type: 'clinical' },
      { id: 5, question: 'ISO 13485质量管理体系如何建立？', type: 'quality' },
      { id: 6, question: '最新的IVD法规政策有哪些变化？', type: 'regulatory' },
      { id: 7, question: '免疫层析技术的优化方向有哪些？', type: 'research' },
      { id: 8, question: 'FDA 510(k)申报的关键要点是什么？', type: 'registration' }
    ],
    
    // 界面状态
    showSessionModal: false,
    loading: false,
    loadingText: '加载中...'
  },

  onLoad(options) {
    console.log('聊天页面加载', options)
    
    // 获取用户信息
    this.setData({
      userInfo: app.globalData.userInfo
    })
    
    // 初始化可用模型
    this.initAvailableModels()
    
    // 加载或创建会话
    if (options.sessionId) {
      this.loadSession(options.sessionId)
    } else {
      this.createNewSession()
    }
    
    // 获取用户订阅信息
    this.loadUserSubscription()
  },

  onShow() {
    console.log('聊天页面显示')
    this.refreshUserSubscription()
  },

  // 初始化可用模型
  initAvailableModels() {
    const subscription = app.globalData.subscription || { tier: 0 }
    const models = app.globalData.availableModels || []
    
    const availableModels = models.map(model => ({
      ...model,
      available: subscription.tier >= model.tier,
      tierName: this.getTierName(model.tier),
      description: this.getModelDescription(model.id)
    }))
    
    this.setData({
      availableModels: availableModels
    })
  },

  // 获取模型描述
  getModelDescription(modelId) {
    const descriptions = {
      'deepseek-v3': '强大的推理能力，适合复杂问题分析',
      'deepseek-r1': '专业的推理模型，逻辑性强',
      'chatgpt': '通用性强，回答自然流畅',
      'gemini': 'Google出品，多模态能力强',
      'qwen3': '阿里出品，中文理解优秀',
      'qwen-max': '最强版本，专业能力突出'
    }
    return descriptions[modelId] || '专业AI模型'
  },

  // 获取订阅等级名称
  getTierName(tier) {
    const tierNames = {
      1: '基础版',
      2: '标准版',
      3: '专业版',
      4: '旗舰版'
    }
    return tierNames[tier] || '高级版'
  },

  // 加载用户订阅信息
  async loadUserSubscription() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserSubscription',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success) {
        const subscription = result.result.subscription
        app.globalData.subscription = subscription
        
        this.setData({
          remainingQuota: subscription.quotaRemaining
        })
        
        // 更新可用模型
        this.initAvailableModels()
      }
    } catch (error) {
      console.error('获取订阅信息失败:', error)
    }
  },

  // 刷新用户订阅信息
  refreshUserSubscription() {
    this.loadUserSubscription()
  },

  // 创建新会话
  async createNewSession() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageChatSession',
        data: {
          action: 'create',
          openid: app.globalData.openid,
          sessionData: {
            title: '新对话',
            consultationType: this.data.selectedConsultationType,
            modelId: this.data.currentModel,
            modelName: this.getModelName(this.data.currentModel)
          }
        }
      })

      if (result.result.success) {
        const session = result.result.session
        this.setData({
          currentSession: {
            ...session,
            consultationTypeText: this.getConsultationTypeText(session.consultationType)
          }
        })
      }
    } catch (error) {
      console.error('创建会话失败:', error)
      util.showError('创建会话失败')
    }
  },

  // 加载会话
  async loadSession(sessionId) {
    this.setData({
      loading: true,
      loadingText: '加载对话中...'
    })

    try {
      // 获取会话信息
      const sessionResult = await wx.cloud.callFunction({
        name: 'manageChatSession',
        data: {
          action: 'get',
          openid: app.globalData.openid,
          sessionId: sessionId
        }
      })

      if (sessionResult.result.success) {
        const session = sessionResult.result.session
        this.setData({
          currentSession: session,
          currentModel: session.modelId,
          selectedConsultationType: session.consultationType
        })

        // 获取消息历史
        const messagesResult = await wx.cloud.callFunction({
          name: 'manageChatSession',
          data: {
            action: 'getMessages',
            openid: app.globalData.openid,
            sessionId: sessionId
          }
        })

        if (messagesResult.result.success) {
          const messages = this.formatMessages(messagesResult.result.messages)
          this.setData({
            messages: messages
          })
          
          // 滚动到底部
          this.scrollToBottom()
        }
      }
    } catch (error) {
      console.error('加载会话失败:', error)
      util.showError('加载对话失败')
    } finally {
      this.setData({
        loading: false
      })
    }
  },

  // 格式化消息
  formatMessages(rawMessages) {
    return rawMessages.map((msg, index) => {
      const content = msg.userMessage || msg.aiResponse
      return {
        id: msg._id || index,
        role: msg.userMessage ? 'user' : 'assistant',
        content: content,
        formattedContent: msg.userMessage ? content : this.formatMessageContent(content),
        modelName: msg.modelName,
        timeText: util.formatTime(new Date(msg.createTime), 'HH:mm'),
        liked: false
      }
    }).reduce((acc, msg) => {
      // 将用户消息和AI回复分开
      if (msg.role === 'user') {
        acc.push(msg)
      } else {
        // 找到对应的用户消息
        const userMsgIndex = acc.findIndex(m => m.role === 'user' && !m.hasResponse)
        if (userMsgIndex >= 0) {
          acc[userMsgIndex].hasResponse = true
        }
        acc.push(msg)
      }
      return acc
    }, [])
  },

  // 输入变化
  onInputChange(e) {
    const value = e.detail.value.trim()
    this.setData({
      inputText: value,
      canSend: value.length > 0
    })
  },

  // 发送消息
  async sendMessage() {
    const message = this.data.inputText.trim()
    if (!message || this.data.isTyping) return

    // 检查登录状态
    if (!app.globalData.openid) {
      util.showError('请先登录')
      return
    }

    // 检查剩余额度
    if (this.data.remainingQuota <= 0) {
      util.showError('对话额度已用完，请升级订阅')
      wx.switchTab({
        url: '/pages/subscription/subscription'
      })
      return
    }

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: message,
      timeText: util.formatTime(new Date(), 'HH:mm')
    }

    const newMessages = [...this.data.messages, userMessage]
    this.setData({
      messages: newMessages,
      inputText: '',
      canSend: false,
      isTyping: true,
      showConsultationSelector: false
    })

    // 滚动到底部
    this.scrollToBottom()

    try {
      // 构建消息历史
      const messageHistory = newMessages.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      }))

      // 调用AI模型
      const result = await wx.cloud.callFunction({
        name: 'callAIModel',
        data: {
          openid: app.globalData.openid,
          modelId: this.data.currentModel,
          messages: messageHistory,
          consultationType: this.data.selectedConsultationType,
          sessionId: this.data.currentSession._id
        }
      })

      if (result.result.success) {
        // 添加AI回复
        const aiMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: result.result.response,
          formattedContent: this.formatMessageContent(result.result.response),
          modelName: this.getModelName(this.data.currentModel),
          timeText: util.formatTime(new Date(), 'HH:mm'),
          liked: false
        }

        this.setData({
          messages: [...newMessages, aiMessage],
          remainingQuota: result.result.remainingQuota,
          isTyping: false
        })

        // 更新会话信息
        this.updateSessionInfo(message)
        
        // 滚动到底部
        this.scrollToBottom()
      } else {
        throw new Error(result.result.error)
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      util.showError('发送失败：' + (error.message || '网络错误'))
      
      // 移除用户消息
      this.setData({
        messages: this.data.messages.slice(0, -1),
        isTyping: false
      })
    }
  },

  // 发送快速问题
  sendQuickQuestion(e) {
    const question = e.currentTarget.dataset.question
    const questionIndex = e.currentTarget.dataset.index
    const questionData = this.data.quickQuestions[questionIndex]

    // 自动设置对应的咨询类型
    if (questionData && questionData.type) {
      const consultationType = this.data.consultationTypes.find(type => type.id === questionData.type)
      if (consultationType) {
        this.setData({
          selectedConsultationType: questionData.type,
          'currentSession.consultationType': questionData.type,
          'currentSession.consultationTypeText': consultationType.name,
          inputPlaceholder: `请输入您关于${consultationType.name}的问题...`
        })
      }
    }

    this.setData({
      inputText: question,
      canSend: true
    })
    this.sendMessage()
  },

  // 更新会话信息
  async updateSessionInfo(lastMessage) {
    if (!this.data.currentSession._id) return

    try {
      // 生成会话标题
      const title = this.generateSessionTitle(lastMessage)
      
      await wx.cloud.callFunction({
        name: 'manageChatSession',
        data: {
          action: 'update',
          openid: app.globalData.openid,
          sessionId: this.data.currentSession._id,
          sessionData: {
            title: title,
            lastMessageTime: new Date(),
            messageCount: this.data.messages.length
          }
        }
      })

      // 更新本地会话标题
      this.setData({
        'currentSession.title': title
      })
    } catch (error) {
      console.error('更新会话信息失败:', error)
    }
  },

  // 生成会话标题
  generateSessionTitle(message) {
    if (message.length <= 20) {
      return message
    }
    return message.substring(0, 20) + '...'
  },

  // 滚动到底部
  scrollToBottom() {
    const query = wx.createSelectorQuery()
    query.select('.chat-messages').boundingClientRect()
    query.selectViewport().scrollOffset()
    query.exec((res) => {
      if (res[0]) {
        this.setData({
          scrollTop: res[0].height
        })
      }
    })
  },

  // 获取模型名称
  getModelName(modelId) {
    const model = this.data.availableModels.find(m => m.id === modelId)
    return model ? model.name : modelId
  },

  // 获取咨询类型文本
  getConsultationTypeText(type) {
    const typeItem = this.data.consultationTypes.find(t => t.id === type)
    return typeItem ? typeItem.name : '通用咨询'
  },

  // 显示模型选择器
  showModelSelector() {
    this.setData({
      showModelModal: true
    })
  },

  // 隐藏模型选择器
  hideModelSelector() {
    this.setData({
      showModelModal: false
    })
  },

  // 选择模型
  selectModel(e) {
    const modelId = e.currentTarget.dataset.model
    const model = this.data.availableModels.find(m => m.id === modelId)
    
    if (!model.available) {
      util.showError('当前订阅不支持此模型，请升级订阅')
      return
    }

    this.setData({
      currentModel: modelId,
      showModelModal: false,
      'currentSession.modelId': modelId,
      'currentSession.modelName': model.name
    })

    util.showSuccess('已切换到 ' + model.name)
  },

  // 切换咨询类型选择器
  toggleConsultationSelector() {
    this.setData({
      showConsultationSelector: !this.data.showConsultationSelector
    })
  },

  // 选择咨询类型
  selectConsultationType(e) {
    const type = e.currentTarget.dataset.type
    const typeItem = this.data.consultationTypes.find(t => t.id === type)
    
    this.setData({
      selectedConsultationType: type,
      'currentSession.consultationType': type,
      'currentSession.consultationTypeText': typeItem.name,
      showConsultationSelector: false,
      inputPlaceholder: `请输入您关于${typeItem.name}的问题...`
    })
  },

  // 显示会话菜单
  showSessionMenu() {
    this.setData({
      showSessionModal: true
    })
  },

  // 隐藏会话菜单
  hideSessionMenu() {
    this.setData({
      showSessionModal: false
    })
  },

  // 新建会话
  newSession() {
    this.setData({
      showSessionModal: false
    })
    
    wx.redirectTo({
      url: '/pages/chat/chat'
    })
  },

  // 复制消息
  copyMessage(e) {
    const content = e.currentTarget.dataset.content
    wx.setClipboardData({
      data: content,
      success: () => {
        util.showSuccess('已复制到剪贴板')
      }
    })
  },

  // 点赞消息
  likeMessage(e) {
    const messageId = e.currentTarget.dataset.id
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, liked: !msg.liked }
      }
      return msg
    })

    this.setData({
      messages: messages
    })

    util.showSuccess('感谢您的反馈')
  },

  // 格式化消息内容，高亮专业术语
  formatMessageContent(content) {
    if (!content) return content

    // 常见IVD术语列表
    const ivdTerms = [
      'ELISA', 'PCR', 'qPCR', 'CLIA', 'POCT', 'NMPA', 'FDA', 'CE', 'GMP', 'ISO13485',
      'Sensitivity', 'Specificity', 'PPV', 'NPV', 'ROC', 'CAPA', 'FMEA', 'Validation', 'Verification',
      '敏感性', '特异性', '阳性预测值', '阴性预测值', '化学发光', '免疫层析', '分子诊断', '即时检验'
    ]

    let formattedContent = content

    // 为术语添加高亮样式和点击事件
    ivdTerms.forEach(term => {
      const regex = new RegExp(`\\b${term}\\b`, 'gi')
      formattedContent = formattedContent.replace(regex, (match) => {
        return `<span class="ivd-term" data-term="${match}" style="color: #2E86AB; font-weight: 500; text-decoration: underline; cursor: pointer;">${match}</span>`
      })
    })

    return formattedContent
  },

  // 解释术语
  async explainTerm(e) {
    const term = e.currentTarget.dataset.term
    if (!term) return

    wx.showLoading({
      title: '查询中...'
    })

    try {
      const result = await wx.cloud.callFunction({
        name: 'explainIVDTerm',
        data: { term: term }
      })

      wx.hideLoading()

      if (result.result.success) {
        const explanation = result.result.explanation
        const content = `${explanation.definition}\n\n${explanation.applications ? '应用领域：' + explanation.applications.join('、') : ''}${explanation.advantages ? '\n优势：' + explanation.advantages.join('、') : ''}`

        wx.showModal({
          title: `${term} - ${explanation.chinese || ''}`,
          content: content,
          showCancel: false,
          confirmText: '知道了'
        })
      } else {
        wx.showToast({
          title: result.result.error || '查询失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '查询失败',
        icon: 'none'
      })
    }
  },

  // 语音输入
  showVoiceInput() {
    util.showToast('语音输入功能开发中')
  },

  // 重命名会话
  renameSession() {
    this.setData({
      showSessionModal: false
    })
    util.showToast('重命名功能开发中')
  },

  // 导出会话
  exportSession() {
    this.setData({
      showSessionModal: false
    })
    util.showToast('导出功能开发中')
  },

  // 清空会话
  async clearSession() {
    this.setData({
      showSessionModal: false
    })

    const confirmed = await util.showConfirm('确定要清空当前对话吗？')
    if (!confirmed) return

    this.setData({
      messages: []
    })

    util.showSuccess('对话已清空')
  },

  // 删除会话
  async deleteSession() {
    this.setData({
      showSessionModal: false
    })

    const confirmed = await util.showConfirm('确定要删除当前对话吗？')
    if (!confirmed) return

    try {
      await wx.cloud.callFunction({
        name: 'manageChatSession',
        data: {
          action: 'delete',
          openid: app.globalData.openid,
          sessionId: this.data.currentSession._id
        }
      })

      util.showSuccess('对话已删除')

      // 返回首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    } catch (error) {
      console.error('删除会话失败:', error)
      util.showError('删除失败')
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业AI咨询对话',
      path: '/pages/chat/chat',
      imageUrl: '/images/share-cover.png'
    }
  }
})
