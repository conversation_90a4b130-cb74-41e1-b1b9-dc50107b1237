// cloudfunctions/login/index.js
// 用户登录云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo } = event
  
  try {
    const openid = wxContext.OPENID
    const appid = wxContext.APPID
    const unionid = wxContext.UNIONID
    
    // 查询用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    const now = new Date()
    
    if (userResult.data.length > 0) {
      // 用户已存在，更新登录信息
      const existingUser = userResult.data[0]
      
      await db.collection('users').doc(existingUser._id).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender,
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          language: userInfo.language,
          lastLoginTime: now,
          updateTime: now
        }
      })
      
      console.log('用户登录成功:', openid)
    } else {
      // 新用户，创建用户记录
      await db.collection('users').add({
        data: {
          openid: openid,
          unionid: unionid,
          appid: appid,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender,
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          language: userInfo.language,
          createTime: now,
          lastLoginTime: now,
          updateTime: now,
          totalChats: 0,
          status: 'active'
        }
      })
      
      console.log('新用户注册成功:', openid)
    }

    // 记录登录日志
    await db.collection('login_logs').add({
      data: {
        openid: openid,
        loginTime: now,
        userAgent: context.userAgent || '',
        ip: context.clientIP || ''
      }
    })

    return {
      success: true,
      openid: openid,
      appid: appid,
      unionid: unionid,
      message: '登录成功'
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      error: error.message,
      message: '登录失败'
    }
  }
}
