// 云函数测试脚本
// 在微信开发者工具控制台中运行此脚本进行基本功能测试

const testCloudFunctions = async () => {
  console.log('开始云函数测试...')
  
  // 测试结果收集
  const testResults = {
    passed: 0,
    failed: 0,
    errors: []
  }
  
  // 测试辅助函数
  const runTest = async (testName, testFunction) => {
    try {
      console.log(`\n🧪 测试: ${testName}`)
      await testFunction()
      console.log(`✅ ${testName} - 通过`)
      testResults.passed++
    } catch (error) {
      console.error(`❌ ${testName} - 失败:`, error.message)
      testResults.failed++
      testResults.errors.push({ test: testName, error: error.message })
    }
  }
  
  // 1. 测试用户登录功能
  await runTest('用户登录功能', async () => {
    const result = await wx.cloud.callFunction({
      name: 'login',
      data: {
        code: 'test_code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      }
    })
    
    if (!result.result.success) {
      throw new Error('登录失败')
    }
    
    console.log('登录结果:', result.result)
  })
  
  // 2. 测试获取用户信息
  await runTest('获取用户信息', async () => {
    const result = await wx.cloud.callFunction({
      name: 'getUserInfo',
      data: {
        openid: 'test_openid'
      }
    })
    
    console.log('用户信息:', result.result)
  })
  
  // 3. 测试获取订阅信息
  await runTest('获取订阅信息', async () => {
    const result = await wx.cloud.callFunction({
      name: 'getUserSubscription',
      data: {
        openid: 'test_openid'
      }
    })
    
    console.log('订阅信息:', result.result)
  })
  
  // 4. 测试AI模型调用
  await runTest('AI模型调用', async () => {
    const result = await wx.cloud.callFunction({
      name: 'callAIModel',
      data: {
        model: 'deepseek-chat',
        messages: [
          { role: 'user', content: '你好，请介绍一下ELISA技术' }
        ],
        consultationType: 'research',
        openid: 'test_openid'
      }
    })
    
    if (!result.result.success) {
      throw new Error('AI调用失败: ' + result.result.error)
    }
    
    console.log('AI响应:', result.result.response.substring(0, 100) + '...')
  })
  
  // 5. 测试IVD知识库
  await runTest('IVD知识库查询', async () => {
    const result = await wx.cloud.callFunction({
      name: 'getIVDKnowledge',
      data: {
        query: 'ELISA',
        category: 'technologies'
      }
    })
    
    if (!result.result.success) {
      throw new Error('知识库查询失败')
    }
    
    console.log('知识库结果:', result.result.knowledge.length, '条记录')
  })
  
  // 6. 测试术语解释
  await runTest('术语解释功能', async () => {
    const result = await wx.cloud.callFunction({
      name: 'explainIVDTerm',
      data: {
        term: 'ELISA'
      }
    })
    
    if (!result.result.success) {
      throw new Error('术语解释失败')
    }
    
    console.log('术语解释:', result.result.explanation.definition.substring(0, 50) + '...')
  })
  
  // 7. 测试聊天会话管理
  await runTest('聊天会话管理', async () => {
    // 创建会话
    const createResult = await wx.cloud.callFunction({
      name: 'manageChatSession',
      data: {
        action: 'create',
        openid: 'test_openid',
        title: '测试会话',
        consultationType: 'research'
      }
    })
    
    if (!createResult.result.success) {
      throw new Error('创建会话失败')
    }
    
    const sessionId = createResult.result.sessionId
    console.log('创建会话ID:', sessionId)
    
    // 获取会话列表
    const listResult = await wx.cloud.callFunction({
      name: 'manageChatSession',
      data: {
        action: 'list',
        openid: 'test_openid'
      }
    })
    
    if (!listResult.result.success) {
      throw new Error('获取会话列表失败')
    }
    
    console.log('会话列表:', listResult.result.sessions.length, '个会话')
  })
  
  // 8. 测试用户统计
  await runTest('用户统计功能', async () => {
    const result = await wx.cloud.callFunction({
      name: 'getUserStats',
      data: {
        openid: 'test_openid'
      }
    })
    
    if (!result.result.success) {
      throw new Error('获取统计失败')
    }
    
    console.log('用户统计:', result.result.stats)
  })
  
  // 输出测试总结
  console.log('\n📊 测试总结:')
  console.log(`✅ 通过: ${testResults.passed}`)
  console.log(`❌ 失败: ${testResults.failed}`)
  console.log(`📈 成功率: ${(testResults.passed / (testResults.passed + testResults.failed) * 100).toFixed(1)}%`)
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败详情:')
    testResults.errors.forEach(error => {
      console.log(`- ${error.test}: ${error.error}`)
    })
  }
  
  return testResults
}

// 性能测试函数
const performanceTest = async () => {
  console.log('\n🚀 开始性能测试...')
  
  const testAIResponse = async () => {
    const startTime = Date.now()
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'callAIModel',
        data: {
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: '简单介绍一下PCR技术' }
          ],
          consultationType: 'research',
          openid: 'test_openid'
        }
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime
      
      console.log(`AI响应时间: ${responseTime}ms`)
      
      if (responseTime > 30000) {
        console.warn('⚠️ AI响应时间超过30秒，可能需要优化')
      } else if (responseTime > 10000) {
        console.log('⚡ AI响应时间较长，建议优化')
      } else {
        console.log('✅ AI响应时间正常')
      }
      
      return responseTime
    } catch (error) {
      console.error('AI性能测试失败:', error)
      return null
    }
  }
  
  // 执行多次测试获取平均响应时间
  const responseTimes = []
  for (let i = 0; i < 3; i++) {
    console.log(`第 ${i + 1} 次性能测试...`)
    const time = await testAIResponse()
    if (time) responseTimes.push(time)
    
    // 等待1秒再进行下一次测试
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  if (responseTimes.length > 0) {
    const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
    console.log(`📊 平均响应时间: ${avgTime.toFixed(0)}ms`)
  }
}

// 数据库连接测试
const databaseTest = async () => {
  console.log('\n💾 开始数据库测试...')
  
  try {
    // 测试数据库写入
    const db = wx.cloud.database()
    const testData = {
      test: true,
      timestamp: new Date(),
      data: '测试数据'
    }
    
    const addResult = await db.collection('test_collection').add({
      data: testData
    })
    
    console.log('✅ 数据库写入成功:', addResult._id)
    
    // 测试数据库读取
    const getResult = await db.collection('test_collection')
      .doc(addResult._id)
      .get()
    
    console.log('✅ 数据库读取成功')
    
    // 清理测试数据
    await db.collection('test_collection')
      .doc(addResult._id)
      .remove()
    
    console.log('✅ 测试数据清理完成')
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error)
  }
}

// 导出测试函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testCloudFunctions,
    performanceTest,
    databaseTest
  }
}

// 如果在浏览器环境中，将函数挂载到全局对象
if (typeof window !== 'undefined') {
  window.cloudFunctionTest = {
    testCloudFunctions,
    performanceTest,
    databaseTest
  }
}

// 使用说明
console.log(`
🧪 云函数测试脚本使用说明:

1. 基本功能测试:
   testCloudFunctions()

2. 性能测试:
   performanceTest()

3. 数据库测试:
   databaseTest()

4. 完整测试:
   (async () => {
     await testCloudFunctions()
     await performanceTest()
     await databaseTest()
   })()

请在微信开发者工具的控制台中运行上述命令进行测试。
`)
