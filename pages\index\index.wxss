/* pages/index/index.wxss */

.page-container {
  padding-bottom: 120rpx;
}

/* 头部欢迎区域 */
.header-section {
  margin-bottom: 40rpx;
}

.welcome-card {
  background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.welcome-content {
  flex: 1;
  z-index: 2;
}

.welcome-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
}

.subscription-status {
  font-size: 24rpx;
  opacity: 0.8;
  padding: 4rpx 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  align-self: flex-start;
}

.welcome-icon {
  width: 120rpx;
  height: 120rpx;
  z-index: 2;
}

/* 区域标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 32rpx;
  padding-left: 20rpx;
}

/* 快速开始区域 */
.quick-start-section {
  margin-bottom: 40rpx;
}

.quick-actions {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F3F4;
  transition: background-color 0.3s ease;
}

.action-item:last-child {
  border-bottom: none;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
  border-radius: 16rpx;
}

.action-icon image {
  width: 48rpx;
  height: 48rpx;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.action-desc {
  font-size: 26rpx;
  color: #6C757D;
}

.action-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 20rpx;
}

.action-arrow image {
  width: 100%;
  height: 100%;
}

/* 专业领域区域 */
.expertise-section {
  margin-bottom: 40rpx;
}

.expertise-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.expertise-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.expertise-item:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.expertise-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 24rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expertise-icon.research {
  background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
}

.expertise-icon.registration {
  background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
}

.expertise-icon.sales {
  background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
}

.expertise-icon.general {
  background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
}

.expertise-icon image {
  width: 48rpx;
  height: 48rpx;
}

.expertise-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 12rpx;
}

.expertise-desc {
  font-size: 24rpx;
  color: #6C757D;
}

/* 使用统计区域 */
.stats-section {
  margin-bottom: 40rpx;
}

.stats-card {
  padding: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6C757D;
}

/* 登录提示区域 */
.login-prompt {
  margin-bottom: 40rpx;
}

.prompt-card {
  text-align: center;
  padding: 60rpx 40rpx;
}

.prompt-content {
  max-width: 500rpx;
  margin: 0 auto;
}

.prompt-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 20rpx;
}

.prompt-desc {
  font-size: 28rpx;
  color: #6C757D;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.prompt-card .btn {
  width: 100%;
  max-width: 400rpx;
}
