// cloudfunctions/database/index.js
// 数据库初始化云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'initDatabase':
        return await initDatabase()
      case 'createIndexes':
        return await createIndexes()
      default:
        return { success: false, error: 'Unknown action' }
    }
  } catch (error) {
    console.error('Database operation error:', error)
    return { success: false, error: error.message }
  }
}

// 初始化数据库集合
async function initDatabase() {
  const collections = [
    'users',           // 用户信息
    'subscriptions',   // 订阅信息
    'chat_sessions',   // 聊天会话
    'chat_messages',   // 聊天消息
    'usage_logs',      // 使用记录
    'payments',        // 支付记录
    'feedback'         // 用户反馈
  ]

  const results = []
  
  for (const collectionName of collections) {
    try {
      // 检查集合是否存在
      const collection = db.collection(collectionName)
      await collection.limit(1).get()
      results.push({ collection: collectionName, status: 'exists' })
    } catch (error) {
      // 集合不存在，创建集合
      try {
        await db.createCollection(collectionName)
        results.push({ collection: collectionName, status: 'created' })
      } catch (createError) {
        results.push({ 
          collection: collectionName, 
          status: 'error', 
          error: createError.message 
        })
      }
    }
  }

  return { success: true, results }
}

// 创建数据库索引
async function createIndexes() {
  const indexes = [
    {
      collection: 'users',
      indexes: [
        { keys: { openid: 1 }, unique: true },
        { keys: { createTime: -1 } }
      ]
    },
    {
      collection: 'subscriptions',
      indexes: [
        { keys: { openid: 1 } },
        { keys: { status: 1 } },
        { keys: { expireTime: 1 } }
      ]
    },
    {
      collection: 'chat_sessions',
      indexes: [
        { keys: { openid: 1 } },
        { keys: { createTime: -1 } },
        { keys: { lastMessageTime: -1 } }
      ]
    },
    {
      collection: 'chat_messages',
      indexes: [
        { keys: { sessionId: 1 } },
        { keys: { openid: 1 } },
        { keys: { createTime: -1 } }
      ]
    },
    {
      collection: 'usage_logs',
      indexes: [
        { keys: { openid: 1 } },
        { keys: { createTime: -1 } },
        { keys: { model: 1 } }
      ]
    },
    {
      collection: 'payments',
      indexes: [
        { keys: { openid: 1 } },
        { keys: { outTradeNo: 1 }, unique: true },
        { keys: { createTime: -1 } }
      ]
    }
  ]

  const results = []
  
  for (const { collection, indexes: collectionIndexes } of indexes) {
    for (const index of collectionIndexes) {
      try {
        await db.collection(collection).createIndex(index)
        results.push({ 
          collection, 
          index: index.keys, 
          status: 'created' 
        })
      } catch (error) {
        results.push({ 
          collection, 
          index: index.keys, 
          status: 'error', 
          error: error.message 
        })
      }
    }
  }

  return { success: true, results }
}
