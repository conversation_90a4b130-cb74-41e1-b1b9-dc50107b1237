# 云开发环境变量配置

## 概述
为了安全地管理API密钥和敏感配置信息，本项目使用腾讯云开发的环境变量功能来存储所有的API Keys和配置参数。

## 环境变量列表

### AI模型API配置
```
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
```

### 微信支付配置
```
WX_PAY_MCHID=your_merchant_id
WX_PAY_KEY=your_pay_key
WX_PAY_CERT_SERIAL_NO=your_cert_serial_no
WX_PAY_PRIVATE_KEY=your_private_key_content
WX_PAY_NOTIFY_URL=https://your_domain.com/pay/notify
```

### 其他配置
```
APP_SECRET=your_app_secret
ENCRYPT_KEY=your_encrypt_key_for_sensitive_data
ADMIN_OPENIDS=openid1,openid2,openid3
```

## 配置步骤

### 1. 在腾讯云开发控制台配置环境变量

1. 登录腾讯云开发控制台
2. 选择环境 `cloudbase-7g8nxwah43c62b19`
3. 进入"环境设置" -> "环境变量"
4. 添加上述所有环境变量

### 2. 获取各平台API Key

#### DeepSeek API
1. 访问 https://platform.deepseek.com/
2. 注册账号并获取API Key
3. 将API Key配置到 `DEEPSEEK_API_KEY`

#### OpenAI API
1. 访问 https://platform.openai.com/
2. 注册账号并获取API Key
3. 将API Key配置到 `OPENAI_API_KEY`

#### Google Gemini API
1. 访问 https://makersuite.google.com/
2. 获取API Key
3. 将API Key配置到 `GEMINI_API_KEY`

#### 阿里云通义千问API
1. 访问 https://dashscope.aliyun.com/
2. 获取API Key
3. 将API Key配置到 `QWEN_API_KEY`

### 3. 微信支付配置

1. 在微信商户平台获取商户号和支付密钥
2. 下载API证书
3. 配置相关环境变量

## 安全注意事项

1. **绝不在代码中硬编码API Key**
2. **定期轮换API密钥**
3. **限制API Key的使用权限**
4. **监控API使用情况**
5. **设置合理的费用预警**

## 环境变量使用示例

在云函数中使用环境变量：

```javascript
// 获取环境变量
const deepseekApiKey = process.env.DEEPSEEK_API_KEY
const openaiApiKey = process.env.OPENAI_API_KEY

// 使用环境变量
const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
  headers: {
    'Authorization': `Bearer ${deepseekApiKey}`,
    'Content-Type': 'application/json'
  },
  // ...
})
```

## 测试配置

配置完成后，可以通过以下方式测试：

1. 部署云函数
2. 在小程序中调用AI聊天功能
3. 检查云函数日志确认API调用成功
4. 监控各平台的API使用情况

## 故障排除

### 常见问题

1. **API Key无效**
   - 检查环境变量名称是否正确
   - 确认API Key是否有效
   - 检查API Key权限设置

2. **网络连接问题**
   - 确认云函数网络配置
   - 检查API服务状态
   - 验证防火墙设置

3. **配额超限**
   - 检查API使用量
   - 升级API套餐
   - 实施使用限制

### 日志监控

在云函数中添加适当的日志记录：

```javascript
console.log('API调用开始:', { model, timestamp: new Date() })
console.log('API调用结果:', { success, usage, timestamp: new Date() })
```

## 更新记录

- 2024-06-24: 初始配置文档创建
- 待更新: 根据实际部署情况更新配置信息
