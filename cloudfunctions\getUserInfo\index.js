// cloudfunctions/getUserInfo/index.js
// 获取用户信息云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    let userInfo = null
    
    if (userResult.data.length > 0) {
      userInfo = userResult.data[0]
    } else {
      // 用户不存在，创建新用户记录
      const createTime = new Date()
      const newUser = {
        openid: openid,
        createTime: createTime,
        lastLoginTime: createTime,
        totalChats: 0,
        status: 'active'
      }
      
      await db.collection('users').add({
        data: newUser
      })
      
      userInfo = newUser
    }

    // 更新最后登录时间
    await db.collection('users').where({
      openid: openid
    }).update({
      data: {
        lastLoginTime: new Date()
      }
    })

    return {
      success: true,
      openid: openid,
      userInfo: userInfo,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    }
  } catch (error) {
    console.error('Get user info error:', error)
    return {
      success: false,
      error: error.message,
      openid: openid
    }
  }
}
