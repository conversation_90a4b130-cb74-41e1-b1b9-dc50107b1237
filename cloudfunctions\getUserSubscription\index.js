// cloudfunctions/getUserSubscription/index.js
// 获取用户订阅信息云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { openid } = event
  const wxContext = cloud.getWXContext()
  const currentOpenid = openid || wxContext.OPENID
  
  try {
    // 查询用户订阅信息
    const subscriptionResult = await db.collection('subscriptions').where({
      openid: currentOpenid,
      status: 'active'
    }).orderBy('createTime', 'desc').limit(1).get()

    let subscription = null
    
    if (subscriptionResult.data.length > 0) {
      const sub = subscriptionResult.data[0]
      const now = new Date()
      
      // 检查订阅是否过期
      if (sub.expireTime && new Date(sub.expireTime) > now) {
        subscription = {
          id: sub._id,
          tier: sub.tier,
          tierName: sub.tierName,
          price: sub.price,
          quotaTotal: sub.quotaTotal,
          quotaUsed: sub.quotaUsed || 0,
          quotaRemaining: (sub.quotaTotal || 0) - (sub.quotaUsed || 0),
          expireTime: sub.expireTime,
          createTime: sub.createTime,
          status: sub.status,
          models: sub.models || []
        }
      } else if (sub.expireTime && new Date(sub.expireTime) <= now) {
        // 订阅已过期，更新状态
        await db.collection('subscriptions').doc(sub._id).update({
          data: {
            status: 'expired',
            updateTime: now
          }
        })
      }
    }
    
    // 如果没有有效订阅，返回默认免费套餐信息
    if (!subscription) {
      subscription = {
        tier: 0,
        tierName: '免费体验',
        price: 0,
        quotaTotal: 10,
        quotaUsed: 0,
        quotaRemaining: 10,
        expireTime: null,
        status: 'free',
        models: ['deepseek-v3']
      }
      
      // 查询免费额度使用情况
      const usageResult = await db.collection('usage_logs').where({
        openid: currentOpenid
      }).count()
      
      if (usageResult.total > 0) {
        const usageDetailResult = await db.collection('usage_logs').where({
          openid: currentOpenid
        }).get()
        
        subscription.quotaUsed = usageDetailResult.data.length
        subscription.quotaRemaining = Math.max(0, subscription.quotaTotal - subscription.quotaUsed)
      }
    }

    return {
      success: true,
      subscription: subscription
    }
  } catch (error) {
    console.error('Get user subscription error:', error)
    return {
      success: false,
      error: error.message,
      subscription: null
    }
  }
}
