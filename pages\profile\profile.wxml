<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-header">
      <view class="avatar-section">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFit"></image>
        <view class="avatar-badge" wx:if="{{subscription.tier > 0}}">
          <text class="badge-text">{{subscription.tierName}}</text>
        </view>
      </view>
      
      <view class="user-info">
        <view class="user-name">{{userInfo.nickName}}</view>
        <view class="user-meta">
          <text class="join-time">{{joinTimeText}}</text>
          <text class="separator">·</text>
          <text class="user-id">ID: {{userIdShort}}</text>
        </view>
        <view class="user-profile" wx:if="{{profileInfo.realName}}">
          <text class="real-name">{{profileInfo.realName}}</text>
          <text class="company" wx:if="{{profileInfo.company}}"> · {{profileInfo.company}}</text>
        </view>
      </view>
      
      <view class="edit-btn" bindtap="editProfile">
        <image class="edit-icon" src="/images/edit-icon.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 订阅状态 -->
    <view class="subscription-status">
      <view class="status-item">
        <view class="status-label">当前订阅</view>
        <view class="status-value {{subscription.status}}">{{subscription.tierName}}</view>
      </view>
      <view class="status-item">
        <view class="status-label">剩余额度</view>
        <view class="status-value">{{subscription.quotaRemaining}}/{{subscription.quotaTotal}}</view>
      </view>
      <view class="status-item" wx:if="{{subscription.expireTime}}">
        <view class="status-label">到期时间</view>
        <view class="status-value">{{subscription.expireTimeText}}</view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="usage-stats">
      <view class="stats-title">使用统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{userStats.totalChats}}</view>
          <view class="stat-label">总对话数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{userStats.monthlyChats}}</view>
          <view class="stat-label">本月对话</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{userStats.favoriteModel}}</view>
          <view class="stat-label">常用模型</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{userStats.consecutiveDays}}</view>
          <view class="stat-label">连续天数</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToSubscription">
        <view class="menu-icon-wrapper subscription">
          <image class="menu-icon" src="/images/subscription-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">订阅管理</view>
          <view class="menu-desc">查看订阅状态，升级套餐</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToChatHistory">
        <view class="menu-icon-wrapper chat">
          <image class="menu-icon" src="/images/chat-history-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">对话历史</view>
          <view class="menu-desc">查看和管理历史对话</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToUsageRecords">
        <view class="menu-icon-wrapper usage">
          <image class="menu-icon" src="/images/usage-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">使用记录</view>
          <view class="menu-desc">查看详细使用统计</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-icon-wrapper settings">
          <image class="menu-icon" src="/images/settings-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">设置</view>
          <view class="menu-desc">个性化设置和偏好</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToFeedback">
        <view class="menu-icon-wrapper feedback">
          <image class="menu-icon" src="/images/feedback-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">意见反馈</view>
          <view class="menu-desc">帮助我们改进产品</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-icon-wrapper about">
          <image class="menu-icon" src="/images/about-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <view class="menu-title">关于我们</view>
          <view class="menu-desc">了解IVD智能顾问</view>
        </view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="/images/arrow-right-icon.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-btn primary" bindtap="startNewChat">
      <image class="action-icon" src="/images/chat-icon.png" mode="aspectFit"></image>
      <text class="action-text">开始对话</text>
    </view>
    
    <view class="action-btn secondary" bindtap="shareApp">
      <image class="action-icon" src="/images/share-icon.png" mode="aspectFit"></image>
      <text class="action-text">分享应用</text>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">IVD智能顾问 v{{appVersion}}</text>
  </view>

  <!-- 个人资料编辑弹窗 -->
  <view class="modal-overlay" wx:if="{{showProfileModal}}" bindtap="hideProfileModal">
    <view class="profile-modal" catchtap="">
      <view class="modal-header">
        <view class="modal-title">编辑个人资料</view>
        <view class="close-btn" bindtap="hideProfileModal">
          <image class="close-icon" src="/images/close-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="modal-content">
        <view class="form-group">
          <view class="label">真实姓名</view>
          <input 
            class="input-field" 
            placeholder="请输入真实姓名"
            value="{{editProfile.realName}}"
            bindinput="onRealNameInput"
          />
        </view>
        
        <view class="form-group">
          <view class="label">手机号码</view>
          <input 
            class="input-field" 
            placeholder="请输入手机号码"
            type="number"
            value="{{editProfile.phone}}"
            bindinput="onPhoneInput"
          />
        </view>
        
        <view class="form-group">
          <view class="label">公司名称</view>
          <input 
            class="input-field" 
            placeholder="请输入公司名称（可选）"
            value="{{editProfile.company}}"
            bindinput="onCompanyInput"
          />
        </view>
        
        <view class="form-group">
          <view class="label">职位</view>
          <input 
            class="input-field" 
            placeholder="请输入职位（可选）"
            value="{{editProfile.position}}"
            bindinput="onPositionInput"
          />
        </view>
        
        <view class="form-group">
          <view class="label">兴趣领域</view>
          <view class="interest-tags">
            <view 
              class="tag {{item.selected ? 'selected' : ''}}"
              wx:for="{{interestOptions}}"
              wx:key="id"
              bindtap="toggleInterest"
              data-id="{{item.id}}"
            >
              {{item.name}}
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="hideProfileModal">取消</button>
        <button 
          class="save-btn {{canSaveProfile ? '' : 'disabled'}}" 
          bindtap="saveProfile"
          disabled="{{!canSaveProfile}}"
        >
          保存
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view>
