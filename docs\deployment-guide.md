# IVD智能顾问 2.0.0 部署指南

## 1. 部署概述

### 1.1 部署架构
```
微信小程序前端
    ↓
腾讯云开发环境
    ├── 云函数 (Node.js)
    ├── 云数据库 (MongoDB)
    ├── 云存储 (文件存储)
    └── 环境变量 (API Keys)
```

### 1.2 环境要求
- **微信开发者工具**: 最新稳定版
- **Node.js**: 14.x 或更高版本
- **腾讯云开发**: 已开通云开发服务
- **微信小程序**: 已注册小程序账号

## 2. 云开发环境配置

### 2.1 创建云开发环境
1. 登录腾讯云控制台
2. 进入云开发服务
3. 创建新环境：`cloudbase-7g8nxwah43c62b19`
4. 选择按量计费模式

### 2.2 配置环境变量
在云开发控制台设置以下环境变量：
```bash
# AI模型API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
QWEN_API_KEY=your_qwen_api_key

# 微信支付配置
WECHAT_PAY_MCHID=your_merchant_id
WECHAT_PAY_KEY=your_pay_key
WECHAT_PAY_CERT_PATH=your_cert_path
```

### 2.3 数据库初始化
创建以下集合：
```javascript
// 用户集合
db.createCollection('users')
db.createCollection('subscriptions')
db.createCollection('chat_sessions')
db.createCollection('chat_messages')
db.createCollection('usage_logs')
db.createCollection('payments')
db.createCollection('feedback')
db.createCollection('user_profiles')
db.createCollection('payment_orders')
db.createCollection('login_logs')
```

## 3. 云函数部署

### 3.1 云函数列表
```
cloudfunctions/
├── login/                    # 用户登录
├── getUserInfo/             # 获取用户信息
├── updateUserInfo/          # 更新用户信息
├── getUserSubscription/     # 获取订阅信息
├── createPaymentOrder/      # 创建支付订单
├── processPayment/          # 处理支付回调
├── callAIModel/            # AI模型调用
├── manageChatSession/       # 聊天会话管理
├── getUserStats/           # 用户统计
├── getIVDKnowledge/        # IVD知识库
└── explainIVDTerm/         # 术语解释
```

### 3.2 部署步骤
1. 在微信开发者工具中右键云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成
4. 在云开发控制台验证函数状态

### 3.3 云函数配置
为每个云函数设置：
- **内存**: 256MB (AI调用函数建议512MB)
- **超时时间**: 30秒 (AI调用函数建议60秒)
- **环境变量**: 继承云环境配置

## 4. 小程序配置

### 4.1 app.json配置
```json
{
  "cloud": true,
  "pages": [
    "pages/index/index",
    "pages/chat/chat",
    "pages/profile/profile",
    "pages/subscription/subscription",
    "pages/login/login"
  ],
  "tabBar": {
    "selectedColor": "#2E86AB",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png"
      },
      {
        "pagePath": "pages/chat/chat",
        "text": "对话",
        "iconPath": "images/chat.png",
        "selectedIconPath": "images/chat-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/profile.png",
        "selectedIconPath": "images/profile-active.png"
      }
    ]
  },
  "permission": {
    "scope.userInfo": {
      "desc": "用于完善用户资料"
    }
  }
}
```

### 4.2 云开发初始化
在app.js中配置：
```javascript
wx.cloud.init({
  env: 'cloudbase-7g8nxwah43c62b19',
  traceUser: true
})
```

## 5. 微信支付配置

### 5.1 商户号配置
1. 登录微信支付商户平台
2. 配置小程序支付
3. 设置支付回调URL
4. 下载API证书

### 5.2 支付参数配置
```javascript
// 在云函数中配置
const paymentConfig = {
  appid: 'your_mini_program_appid',
  mchid: process.env.WECHAT_PAY_MCHID,
  key: process.env.WECHAT_PAY_KEY,
  notify_url: 'https://your-domain.com/payment/notify'
}
```

## 6. 域名配置

### 6.1 服务器域名配置
在小程序管理后台配置以下域名：
```
request合法域名:
- https://api.deepseek.com
- https://api.openai.com
- https://generativelanguage.googleapis.com
- https://dashscope.aliyuncs.com

uploadFile合法域名:
- https://your-cloud-storage-domain.com

downloadFile合法域名:
- https://your-cloud-storage-domain.com
```

## 7. 性能优化

### 7.1 代码优化
- 启用代码压缩
- 图片资源优化
- 按需加载组件
- 减少包体积

### 7.2 云函数优化
- 合理设置内存和超时时间
- 使用连接池管理数据库连接
- 实现缓存机制
- 异步处理非关键操作

### 7.3 数据库优化
- 创建必要的索引
- 合理设计数据结构
- 实现数据分页
- 定期清理过期数据

## 8. 监控和日志

### 8.1 云函数监控
- 调用次数统计
- 错误率监控
- 响应时间监控
- 资源使用监控

### 8.2 日志配置
```javascript
// 在云函数中添加日志
console.log('Function started:', {
  functionName: context.functionName,
  requestId: context.requestId,
  timestamp: new Date().toISOString()
})
```

## 9. 安全配置

### 9.1 访问控制
- 配置云函数访问权限
- 设置数据库安全规则
- 启用API密钥保护

### 9.2 数据安全
- 敏感数据加密存储
- 定期备份数据
- 设置访问日志

## 10. 发布流程

### 10.1 预发布检查
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 兼容性测试通过

### 10.2 发布步骤
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在小程序管理后台提交审核
4. 等待微信审核通过
5. 发布上线

### 10.3 发布后监控
- 监控用户反馈
- 观察性能指标
- 跟踪错误日志
- 准备热修复方案

## 11. 回滚方案

### 11.1 小程序回滚
- 在管理后台回滚到上一版本
- 通知用户更新

### 11.2 云函数回滚
- 在云开发控制台回滚函数版本
- 恢复环境变量配置

### 11.3 数据库回滚
- 从备份恢复数据
- 执行数据修复脚本

## 12. 运维建议

### 12.1 定期维护
- 每周检查系统状态
- 每月分析性能报告
- 季度进行安全审计

### 12.2 容量规划
- 监控用户增长趋势
- 预估资源需求
- 及时扩容升级

### 12.3 故障处理
- 建立故障响应流程
- 准备应急联系方式
- 制定故障恢复预案
