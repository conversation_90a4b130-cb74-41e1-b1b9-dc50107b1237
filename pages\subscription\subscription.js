// pages/subscription/subscription.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    currentSubscription: {
      tierName: '免费体验',
      status: 'free',
      statusText: '免费',
      quotaTotal: 10,
      quotaUsed: 0,
      quotaRemaining: 10,
      expireTime: null,
      expireTimeText: ''
    },
    quotaPercentage: 0,
    subscriptionPlans: [
      {
        id: 1,
        name: '基础版',
        price: 9.9,
        originalPrice: null,
        discount: null,
        quota: 100,
        models: ['deepseek-v3', 'deepseek-r1'],
        features: true,
        priority: false,
        popular: false
      },
      {
        id: 2,
        name: '标准版',
        price: 19.9,
        originalPrice: 29.9,
        discount: '7',
        quota: 300,
        models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'qwen3'],
        features: true,
        priority: false,
        popular: true
      },
      {
        id: 3,
        name: '专业版',
        price: 29.9,
        originalPrice: 39.9,
        discount: '7.5',
        quota: 500,
        models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'gemini', 'qwen3'],
        features: true,
        priority: true,
        popular: false
      },
      {
        id: 4,
        name: '旗舰版',
        price: 99.9,
        originalPrice: 129.9,
        discount: '7.7',
        quota: 2000,
        models: ['deepseek-v3', 'deepseek-r1', 'chatgpt', 'gemini', 'qwen3', 'qwen-max'],
        features: true,
        priority: true,
        popular: false
      }
    ],
    modelNames: {
      'deepseek-v3': 'DeepSeek-V3',
      'deepseek-r1': 'DeepSeek-R1',
      'chatgpt': 'ChatGPT',
      'gemini': 'Gemini',
      'qwen3': 'Qwen3',
      'qwen-max': 'Qwen Max'
    },
    selectedPlan: null,
    selectedPlanInfo: null,
    paymentMethod: 'wechat',
    agreedToTerms: false,
    canSubscribe: false,
    subscriptionHistory: [],
    faqList: [
      {
        id: 1,
        question: '订阅后多久生效？',
        answer: '支付成功后立即生效，您可以马上使用所有订阅功能。',
        expanded: false
      },
      {
        id: 2,
        question: '可以随时取消订阅吗？',
        answer: '可以随时取消订阅，取消后不会再自动续费，但当前订阅期内仍可正常使用。',
        expanded: false
      },
      {
        id: 3,
        question: '支持退款吗？',
        answer: '支持7天无理由退款，退款金额将原路返回到您的支付账户。',
        expanded: false
      },
      {
        id: 4,
        question: '额度用完了怎么办？',
        answer: '额度用完后可以升级套餐或等待下个月自动续费重置额度。',
        expanded: false
      }
    ],
    loading: false,
    loadingText: '处理中...'
  },

  onLoad(options) {
    console.log('订阅页面加载')
    this.loadCurrentSubscription()
    this.loadSubscriptionHistory()
  },

  onShow() {
    console.log('订阅页面显示')
    this.refreshSubscriptionStatus()
  },

  // 加载当前订阅信息
  async loadCurrentSubscription() {
    if (!app.globalData.openid) {
      util.showError('请先登录')
      return
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserSubscription',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success && result.result.subscription) {
        const subscription = result.result.subscription
        const statusMap = {
          'active': '有效',
          'expired': '已过期',
          'free': '免费'
        }

        this.setData({
          currentSubscription: {
            tierName: subscription.tierName,
            status: subscription.status,
            statusText: statusMap[subscription.status] || '未知',
            quotaTotal: subscription.quotaTotal,
            quotaUsed: subscription.quotaUsed,
            quotaRemaining: subscription.quotaRemaining,
            expireTime: subscription.expireTime,
            expireTimeText: subscription.expireTime ? 
              util.formatTime(new Date(subscription.expireTime), 'YYYY-MM-DD') : ''
          }
        })

        // 计算额度使用百分比
        const percentage = subscription.quotaTotal > 0 ? 
          (subscription.quotaUsed / subscription.quotaTotal) * 100 : 0
        this.setData({
          quotaPercentage: Math.min(percentage, 100)
        })

        // 更新全局订阅信息
        app.globalData.subscription = subscription
      }
    } catch (error) {
      console.error('获取订阅信息失败:', error)
    }
  },

  // 加载订阅历史
  async loadSubscriptionHistory() {
    if (!app.globalData.openid) return

    try {
      const result = await wx.cloud.callFunction({
        name: 'getSubscriptionHistory',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success) {
        const history = result.result.history.map(item => ({
          ...item,
          createTimeText: util.formatTime(new Date(item.createTime), 'MM-DD HH:mm'),
          statusText: this.getStatusText(item.status)
        }))

        this.setData({
          subscriptionHistory: history
        })
      }
    } catch (error) {
      console.error('获取订阅历史失败:', error)
    }
  },

  // 刷新订阅状态
  refreshSubscriptionStatus() {
    this.loadCurrentSubscription()
  },

  // 选择套餐
  selectPlan(e) {
    const planId = e.currentTarget.dataset.id
    const planInfo = this.data.subscriptionPlans.find(plan => plan.id === planId)
    
    this.setData({
      selectedPlan: planId,
      selectedPlanInfo: planInfo
    })
    
    this.checkCanSubscribe()
  },

  // 选择支付方式
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method
    this.setData({
      paymentMethod: method
    })
    this.checkCanSubscribe()
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreedToTerms: !this.data.agreedToTerms
    })
    this.checkCanSubscribe()
  },

  // 检查是否可以订阅
  checkCanSubscribe() {
    const canSubscribe = this.data.selectedPlan && 
                        this.data.paymentMethod && 
                        this.data.agreedToTerms
    this.setData({
      canSubscribe: canSubscribe
    })
  },

  // 订阅
  async subscribe() {
    if (!this.data.canSubscribe) return

    if (!app.globalData.openid) {
      util.showError('请先登录')
      return
    }

    this.setData({
      loading: true,
      loadingText: '创建订单中...'
    })

    try {
      // 创建订单
      const orderResult = await wx.cloud.callFunction({
        name: 'createSubscriptionOrder',
        data: {
          openid: app.globalData.openid,
          planId: this.data.selectedPlan,
          paymentMethod: this.data.paymentMethod
        }
      })

      if (!orderResult.result.success) {
        throw new Error(orderResult.result.error || '创建订单失败')
      }

      const order = orderResult.result.order

      this.setData({
        loadingText: '发起支付中...'
      })

      // 发起微信支付
      const payResult = await wx.requestPayment({
        timeStamp: order.timeStamp,
        nonceStr: order.nonceStr,
        package: order.package,
        signType: order.signType,
        paySign: order.paySign
      })

      console.log('支付成功:', payResult)
      
      this.setData({
        loading: false
      })

      util.showSuccess('订阅成功')
      
      // 刷新订阅状态
      setTimeout(() => {
        this.loadCurrentSubscription()
      }, 1000)

    } catch (error) {
      console.error('订阅失败:', error)
      
      this.setData({
        loading: false
      })

      if (error.errMsg && error.errMsg.includes('cancel')) {
        util.showToast('支付已取消')
      } else {
        util.showError('订阅失败：' + (error.message || error.errMsg || '未知错误'))
      }
    }
  },

  // 切换FAQ展开状态
  toggleFaq(e) {
    const faqId = e.currentTarget.dataset.id
    const updatedFaqList = this.data.faqList.map(item => {
      if (item.id === faqId) {
        return { ...item, expanded: !item.expanded }
      }
      return item
    })
    
    this.setData({
      faqList: updatedFaqList
    })
  },

  // 显示订阅条款
  showSubscriptionTerms() {
    wx.navigateTo({
      url: '/pages/agreement/agreement?type=subscription'
    })
  },

  // 显示退款政策
  showRefundPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/agreement?type=refund'
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'active': '有效',
      'expired': '已过期',
      'cancelled': '已取消',
      'pending': '待支付'
    }
    return statusMap[status] || '未知'
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业AI咨询订阅服务',
      path: '/pages/subscription/subscription',
      imageUrl: '/images/share-cover.png'
    }
  }
})
