// pages/profile/profile.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    userInfo: null,
    profileInfo: {},
    subscription: {
      tier: 0,
      tierName: '免费版',
      status: 'free',
      quotaRemaining: 0,
      quotaTotal: 0,
      expireTime: null,
      expireTimeText: ''
    },
    userStats: {
      totalChats: 0,
      monthlyChats: 0,
      favoriteModel: 'DeepSeek-V3',
      consecutiveDays: 0
    },
    joinTimeText: '',
    userIdShort: '',
    appVersion: '2.0.0',
    
    // 编辑资料相关
    showProfileModal: false,
    editProfile: {
      realName: '',
      phone: '',
      company: '',
      position: ''
    },
    interestOptions: [
      { id: 'research', name: '产品研发', selected: false },
      { id: 'registration', name: '注册申报', selected: false },
      { id: 'sales', name: '市场销售', selected: false },
      { id: 'quality', name: '质量管理', selected: false },
      { id: 'regulatory', name: '法规事务', selected: false },
      { id: 'clinical', name: '临床试验', selected: false },
      { id: 'manufacturing', name: '生产制造', selected: false },
      { id: 'business', name: '商务合作', selected: false }
    ],
    canSaveProfile: false,
    
    loading: false,
    loadingText: '加载中...'
  },

  onLoad() {
    console.log('个人中心页面加载')
    this.initUserInfo()
    this.loadUserData()
  },

  onShow() {
    console.log('个人中心页面显示')
    this.refreshUserData()
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        userIdShort: this.generateShortId(app.globalData.openid)
      })
    }
  },

  // 生成短ID
  generateShortId(openid) {
    if (!openid) return ''
    return openid.substring(openid.length - 8).toUpperCase()
  },

  // 加载用户数据
  async loadUserData() {
    this.setData({
      loading: true,
      loadingText: '加载用户信息...'
    })

    try {
      await Promise.all([
        this.loadUserProfile(),
        this.loadUserSubscription(),
        this.loadUserStats()
      ])
    } catch (error) {
      console.error('加载用户数据失败:', error)
      util.showError('加载失败')
    } finally {
      this.setData({
        loading: false
      })
    }
  },

  // 刷新用户数据
  refreshUserData() {
    this.loadUserSubscription()
    this.loadUserStats()
  },

  // 加载用户资料
  async loadUserProfile() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserProfile',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success) {
        const profile = result.result.profile
        this.setData({
          profileInfo: profile,
          joinTimeText: util.formatTime(new Date(profile.createTime), 'YYYY年MM月加入')
        })

        // 更新编辑表单
        this.setData({
          editProfile: {
            realName: profile.realName || '',
            phone: profile.phone || '',
            company: profile.company || '',
            position: profile.position || ''
          }
        })

        // 更新兴趣选择
        if (profile.interests && profile.interests.length > 0) {
          const interestOptions = this.data.interestOptions.map(option => ({
            ...option,
            selected: profile.interests.includes(option.id)
          }))
          this.setData({
            interestOptions: interestOptions
          })
        }
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
    }
  },

  // 加载用户订阅信息
  async loadUserSubscription() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserSubscription',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success) {
        const subscription = result.result.subscription
        app.globalData.subscription = subscription
        
        this.setData({
          subscription: {
            ...subscription,
            tierName: this.getTierName(subscription.tier),
            status: subscription.tier > 0 ? 'active' : 'free',
            expireTimeText: subscription.expireTime ? 
              util.formatTime(new Date(subscription.expireTime), 'YYYY-MM-DD') : ''
          }
        })
      }
    } catch (error) {
      console.error('获取订阅信息失败:', error)
    }
  },

  // 加载用户统计
  async loadUserStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          openid: app.globalData.openid
        }
      })

      if (result.result.success) {
        this.setData({
          userStats: result.result.stats
        })
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
    }
  },

  // 获取订阅等级名称
  getTierName(tier) {
    const tierNames = {
      0: '免费版',
      1: '基础版',
      2: '标准版',
      3: '专业版',
      4: '旗舰版'
    }
    return tierNames[tier] || '未知版本'
  },

  // 编辑个人资料
  editProfile() {
    this.setData({
      showProfileModal: true
    })
  },

  // 隐藏资料编辑弹窗
  hideProfileModal() {
    this.setData({
      showProfileModal: false
    })
  },

  // 输入事件处理
  onRealNameInput(e) {
    this.setData({
      'editProfile.realName': e.detail.value
    })
    this.checkCanSave()
  },

  onPhoneInput(e) {
    this.setData({
      'editProfile.phone': e.detail.value
    })
    this.checkCanSave()
  },

  onCompanyInput(e) {
    this.setData({
      'editProfile.company': e.detail.value
    })
    this.checkCanSave()
  },

  onPositionInput(e) {
    this.setData({
      'editProfile.position': e.detail.value
    })
    this.checkCanSave()
  },

  // 切换兴趣标签
  toggleInterest(e) {
    const interestId = e.currentTarget.dataset.id
    const interestOptions = this.data.interestOptions.map(option => {
      if (option.id === interestId) {
        return { ...option, selected: !option.selected }
      }
      return option
    })
    
    this.setData({
      interestOptions: interestOptions
    })
    this.checkCanSave()
  },

  // 检查是否可以保存
  checkCanSave() {
    const { realName, phone } = this.data.editProfile
    const hasSelectedInterests = this.data.interestOptions.some(option => option.selected)
    
    this.setData({
      canSaveProfile: realName.trim().length > 0 && 
                     phone.trim().length > 0 && 
                     hasSelectedInterests
    })
  },

  // 保存个人资料
  async saveProfile() {
    if (!this.data.canSaveProfile) return

    this.setData({
      loading: true,
      loadingText: '保存中...'
    })

    try {
      const selectedInterests = this.data.interestOptions
        .filter(option => option.selected)
        .map(option => option.id)

      const result = await wx.cloud.callFunction({
        name: 'updateUserProfile',
        data: {
          openid: app.globalData.openid,
          profileData: {
            ...this.data.editProfile,
            interests: selectedInterests
          }
        }
      })

      if (result.result.success) {
        util.showSuccess('保存成功')
        this.setData({
          showProfileModal: false
        })
        
        // 重新加载用户资料
        await this.loadUserProfile()
      } else {
        throw new Error(result.result.error)
      }
    } catch (error) {
      console.error('保存个人资料失败:', error)
      util.showError('保存失败：' + (error.message || '网络错误'))
    } finally {
      this.setData({
        loading: false
      })
    }
  },

  // 页面跳转方法
  goToSubscription() {
    wx.switchTab({
      url: '/pages/subscription/subscription'
    })
  },

  goToChatHistory() {
    wx.navigateTo({
      url: '/pages/chat-history/chat-history'
    })
  },

  goToUsageRecords() {
    wx.navigateTo({
      url: '/pages/usage-records/usage-records'
    })
  },

  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 开始新对话
  startNewChat() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 分享应用
  shareApp() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 退出登录
  async logout() {
    const confirmed = await util.showConfirm('确定要退出登录吗？')
    if (!confirmed) return

    try {
      // 清除本地数据
      app.globalData.openid = null
      app.globalData.userInfo = null
      app.globalData.subscription = null
      
      // 清除本地存储
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('openid')
      
      util.showSuccess('已退出登录')
      
      // 跳转到登录页面
      wx.reLaunch({
        url: '/pages/login/login'
      })
    } catch (error) {
      console.error('退出登录失败:', error)
      util.showError('退出失败')
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业的体外诊断AI助手',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    }
  },

  onShareTimeline() {
    return {
      title: 'IVD智能顾问 - 专业的体外诊断AI助手',
      imageUrl: '/images/share-cover.png'
    }
  }
})
