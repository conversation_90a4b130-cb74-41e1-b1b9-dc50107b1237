<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 顶部Logo区域 -->
  <view class="logo-section">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <view class="app-name">IVD智能顾问</view>
    <view class="app-slogan">您的专属研发、注册、销售顾问</view>
  </view>

  <!-- 登录表单区域 -->
  <view class="login-form">
    <view class="form-title">欢迎使用</view>
    <view class="form-subtitle">请授权登录以享受完整服务</view>

    <!-- 微信一键登录 -->
    <button 
      class="login-btn wx-login-btn" 
      open-type="getUserProfile"
      bindgetuserprofile="onGetUserProfile"
      wx:if="{{!userInfo}}"
    >
      <image class="btn-icon" src="/images/wechat-icon.png" mode="aspectFit"></image>
      <text class="btn-text">微信一键登录</text>
    </button>

    <!-- 已登录状态 -->
    <view class="user-info-card" wx:if="{{userInfo}}">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <view class="nickname">{{userInfo.nickName}}</view>
        <view class="login-time">登录时间：{{loginTime}}</view>
      </view>
      <view class="status-badge">已登录</view>
    </view>

    <!-- 用户资料完善 -->
    <view class="profile-section" wx:if="{{userInfo && !profileCompleted}}">
      <view class="section-title">完善个人资料</view>
      <view class="form-group">
        <view class="label">真实姓名</view>
        <input 
          class="input-field" 
          placeholder="请输入真实姓名" 
          value="{{profile.realName}}"
          bindinput="onRealNameInput"
        />
      </view>
      
      <view class="form-group">
        <view class="label">手机号码</view>
        <input 
          class="input-field" 
          placeholder="请输入手机号码" 
          type="number"
          value="{{profile.phone}}"
          bindinput="onPhoneInput"
        />
      </view>
      
      <view class="form-group">
        <view class="label">公司名称</view>
        <input 
          class="input-field" 
          placeholder="请输入公司名称" 
          value="{{profile.company}}"
          bindinput="onCompanyInput"
        />
      </view>
      
      <view class="form-group">
        <view class="label">职位</view>
        <input 
          class="input-field" 
          placeholder="请输入职位" 
          value="{{profile.position}}"
          bindinput="onPositionInput"
        />
      </view>
      
      <view class="form-group">
        <view class="label">关注领域</view>
        <view class="interest-tags">
          <view 
            class="tag {{item.selected ? 'selected' : ''}}" 
            wx:for="{{interestOptions}}" 
            wx:key="id"
            bindtap="onInterestTap"
            data-id="{{item.id}}"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <button class="save-profile-btn" bindtap="saveProfile" disabled="{{!canSaveProfile}}">
        保存资料
      </button>
    </view>

    <!-- 服务协议 -->
    <view class="agreement-section">
      <view class="agreement-text">
        登录即表示您同意
        <text class="link" bindtap="showUserAgreement">《用户协议》</text>
        和
        <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>

    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="section-title">核心功能</view>
      <view class="feature-list">
        <view class="feature-item">
          <image class="feature-icon" src="/images/ai-icon.png" mode="aspectFit"></image>
          <view class="feature-content">
            <view class="feature-title">多AI模型对话</view>
            <view class="feature-desc">支持DeepSeek、ChatGPT、Gemini等多种AI模型</view>
          </view>
        </view>
        
        <view class="feature-item">
          <image class="feature-icon" src="/images/expert-icon.png" mode="aspectFit"></image>
          <view class="feature-content">
            <view class="feature-title">专业领域咨询</view>
            <view class="feature-desc">研发、注册、销售等IVD专业领域指导</view>
          </view>
        </view>
        
        <view class="feature-item">
          <image class="feature-icon" src="/images/cloud-icon.png" mode="aspectFit"></image>
          <view class="feature-content">
            <view class="feature-title">云端数据同步</view>
            <view class="feature-desc">聊天记录云端保存，多设备同步访问</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions" wx:if="{{userInfo}}">
    <button class="action-btn secondary" bindtap="logout">退出登录</button>
    <button class="action-btn primary" bindtap="goToHome">进入应用</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view>
