<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card card">
      <view class="welcome-content">
        <view class="welcome-title">欢迎使用 IVD智能顾问</view>
        <view class="welcome-subtitle">您的专属研发、注册、销售顾问</view>
        <view class="user-info" wx:if="{{userInfo}}">
          <text class="user-name">{{userInfo.nickName || '用户'}}</text>
          <text class="subscription-status">{{subscriptionText}}</text>
        </view>
      </view>
      <image class="welcome-icon" src="/images/logo.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 快速开始区域 -->
  <view class="quick-start-section">
    <view class="section-title">快速开始</view>
    <view class="quick-actions">
      <view class="action-item" bindtap="startChat">
        <view class="action-icon">
          <image src="/images/chat-icon.png" mode="aspectFit"></image>
        </view>
        <view class="action-content">
          <view class="action-title">开始咨询</view>
          <view class="action-desc">与AI顾问即时对话</view>
        </view>
        <view class="action-arrow">
          <image src="/images/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="action-item" bindtap="viewModels">
        <view class="action-icon">
          <image src="/images/model-icon.png" mode="aspectFit"></image>
        </view>
        <view class="action-content">
          <view class="action-title">AI模型</view>
          <view class="action-desc">选择专业AI模型</view>
        </view>
        <view class="action-arrow">
          <image src="/images/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="action-item" bindtap="viewSubscription">
        <view class="action-icon">
          <image src="/images/subscription-icon.png" mode="aspectFit"></image>
        </view>
        <view class="action-content">
          <view class="action-title">订阅套餐</view>
          <view class="action-desc">升级获得更多功能</view>
        </view>
        <view class="action-arrow">
          <image src="/images/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 专业领域区域 -->
  <view class="expertise-section">
    <view class="section-title">专业领域</view>
    <view class="expertise-grid">
      <view class="expertise-item" bindtap="consultResearch">
        <view class="expertise-icon research">
          <image src="/images/research-icon.png" mode="aspectFit"></image>
        </view>
        <view class="expertise-title">产品研发</view>
        <view class="expertise-desc">研发流程指导</view>
      </view>

      <view class="expertise-item" bindtap="consultRegistration">
        <view class="expertise-icon registration">
          <image src="/images/registration-icon.png" mode="aspectFit"></image>
        </view>
        <view class="expertise-title">注册申报</view>
        <view class="expertise-desc">申报要点解析</view>
      </view>

      <view class="expertise-item" bindtap="consultSales">
        <view class="expertise-icon sales">
          <image src="/images/sales-icon.png" mode="aspectFit"></image>
        </view>
        <view class="expertise-title">市场销售</view>
        <view class="expertise-desc">销售策略建议</view>
      </view>

      <view class="expertise-item" bindtap="consultGeneral">
        <view class="expertise-icon general">
          <image src="/images/general-icon.png" mode="aspectFit"></image>
        </view>
        <view class="expertise-title">综合咨询</view>
        <view class="expertise-desc">全方位专业支持</view>
      </view>
    </view>
  </view>

  <!-- 使用统计区域 -->
  <view class="stats-section" wx:if="{{userInfo}}">
    <view class="section-title">使用统计</view>
    <view class="stats-card card">
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{stats.totalChats || 0}}</view>
          <view class="stat-label">总咨询次数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.quotaRemaining || 0}}</view>
          <view class="stat-label">剩余额度</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.favoriteModel || 'DeepSeek-V3'}}</view>
          <view class="stat-label">常用模型</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 登录提示 -->
  <view class="login-prompt" wx:if="{{!userInfo}}">
    <view class="prompt-card card">
      <view class="prompt-content">
        <view class="prompt-title">立即登录，开启智能咨询</view>
        <view class="prompt-desc">登录后可享受个性化AI顾问服务</view>
        <button class="btn btn-primary" bindtap="goToLogin">微信一键登录</button>
      </view>
    </view>
  </view>
</view>
